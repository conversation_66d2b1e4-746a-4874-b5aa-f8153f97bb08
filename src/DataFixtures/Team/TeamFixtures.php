<?php

namespace App\DataFixtures\Team;

use App\Entity\Team\Team;
use App\DataFixtures\Company\CompanyFixtures;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\Uid\Uuid;

class TeamFixtures extends Fixture implements DependentFixtureInterface
{
    private function findExistingTeam(ObjectManager $manager, string $name): ?Team
    {
        return $manager->getRepository(Team::class)->findOneBy(['name' => $name]);
    }

    public function load(ObjectManager $manager): void
    {
        $companies = [
            ['name' => 'Charisma Sport Center', 'uuid' => '43e814c7-4af5-4484-97e9-7d20e8e97b46'],
            ['name' => 'El Elyon Football Académy', 'uuid' => 'acfc2abb-3bf8-4a30-88db-1f5114443172'],
            ['name' => 'Zamo Sports Academy International', 'uuid' => '2817d896-a60a-4704-a73d-bcebf817fd99'],
            ['name' => 'African Roots Association (aras)', 'uuid' => '508a3718-469b-40da-8079-6a54be1b0cd0'],
            ['name' => 'Asec', 'uuid' => '836d8364-5b23-4fc8-a7a7-a12458c28a8e'],
            ['name' => 'Shiny Flame Fa', 'uuid' => 'ee3c5fea-fd38-43cc-8ded-39639546a7bd'],
            ['name' => 'Fc Douala', 'uuid' => '01310020-ed5e-434c-b074-4ab5121a1fb8'],
            ['name' => 'As David Mbengue', 'uuid' => 'dd1dd5ab-bce7-4e4d-ac71-e74cb96dd2cd'],
            ['name' => 'Événementiel Sport', 'uuid' => '8cb6a894-044c-4daa-872c-c74230ce361f']
        ];

        $categories = ['u13', 'u14', 'u15', 'u16', 'u17'];

        foreach ($companies as $companyData) {
            // Create one team per category for each company
            foreach ($categories as $category) {
                $teamName = $companyData['name'] . ' ' . strtoupper($category);
                $team = $this->findExistingTeam($manager, $teamName);
                
                if (!$team) {
                    $team = new Team();
                    // UUID is only set for new teams
                }
                
                // Update team properties
                $team->setName($companyData['name'] . ' ' . strtoupper($category));
                $team->setCategory($this->getReference('category_' . $category));
                $team->setCompany($this->getReference('company_' . $companyData['uuid']));
                $team->setActive(true);
                $team->setUuid(Uuid::v4());

                $manager->persist($team);
                $this->addReference('team_' . $companyData['uuid'] . '_' . $category, $team);
            }
        }

        $manager->flush();
    }

    public function getDependencies(): array
    {
        return [
            TeamCategoryFixtures::class,
            CompanyFixtures::class,
        ];
    }
}