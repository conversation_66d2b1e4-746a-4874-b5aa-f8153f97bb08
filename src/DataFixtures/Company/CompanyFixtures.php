<?php

namespace App\DataFixtures\Company;

use App\Entity\Company\Company;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class CompanyFixtures extends Fixture
{
    private function findExistingCompany(ObjectManager $manager, string $uuid): ?Company
    {
        return $manager->getRepository(Company::class)->findOneBy(['uuid' => $uuid]);
    }

    public function load(ObjectManager $manager): void
    {
        $companies = [
            [
                'name' => 'Charisma Sport Center',
                'uuid' => '43e814c7-4af5-4484-97e9-7d20e8e97b46'
            ],
            [
                'name' => 'El Elyon Football Académy',
                'uuid' => 'acfc2abb-3bf8-4a30-88db-1f5114443172'
            ],
            [
                'name' => 'Zamo Sports Academy International',
                'uuid' => '2817d896-a60a-4704-a73d-bcebf817fd99'
            ],
            [
                'name' => 'African Roots Association (aras)',
                'uuid' => '508a3718-469b-40da-8079-6a54be1b0cd0'
            ],
            [
                'name' => 'Asec',
                'uuid' => '836d8364-5b23-4fc8-a7a7-a12458c28a8e'
            ],
            [
                'name' => 'Shiny Flame Fa',
                'uuid' => 'ee3c5fea-fd38-43cc-8ded-39639546a7bd'
            ],
            [
                'name' => 'Fc Douala',
                'uuid' => '01310020-ed5e-434c-b074-4ab5121a1fb8'
            ],
            [
                'name' => 'As David Mbengue',
                'uuid' => 'dd1dd5ab-bce7-4e4d-ac71-e74cb96dd2cd'
            ],
            [
                'name' => 'Événementiel Sport',
                'uuid' => '8cb6a894-044c-4daa-872c-c74230ce361f'
            ]
        ];

        foreach ($companies as $companyData) {
            $company = $this->findExistingCompany($manager, $companyData['uuid']);
            
            if (!$company) {
                $company = new Company();
                // UUID is only set for new companies
            }
            
            // Update company properties
            $company->setName($companyData['name']);
            $company->setUuid($companyData['uuid']);
            $company->setActive(true);
            
            $manager->persist($company);
            $this->addReference('company_' . $companyData['uuid'], $company);
        }

        $manager->flush();
    }
}