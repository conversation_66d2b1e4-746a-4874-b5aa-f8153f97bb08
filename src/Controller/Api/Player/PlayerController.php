<?php

namespace App\Controller\Api\Player;

use App\Service\Player\Implementation\PlayerService;
use App\Controller\Api\Shared\AbstractBaseApiController;
use App\Dto\GenericResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use FOS\RestBundle\Controller\Annotations as Rest;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Security;

#[Rest\Route('/players')]
#[OA\Tag(name: 'Players')]
class PlayerController extends AbstractBaseApiController
{
    private PlayerService $service;

    public function __construct(PlayerService $service)
    {
        $this->service = $service;
    }

    /**
     * Create a new player
     */
    #[Rest\Post('/create')]
    #[OA\Tag(name: 'Players')]
    #[OA\Post(
        path: '/api/v2/players/create',
        summary: 'Create a new player',
        description: 'Creates a new player linked to a license and team'
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            type: "object",
            required: ["teamId", "firstName", "lastName", "position"],
            properties: [
                new OA\Property(property: 'teamId', type: 'integer', example: 1, description: 'ID of the team'),
                new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                new OA\Property(property: 'position', type: 'string', example: 'Forward', description: 'Player position (Forward, Midfielder, Defender, Goalkeeper)'),
                new OA\Property(property: 'jerseyNumber', type: 'integer', example: 10, description: 'Jersey number (1-99)'),
                new OA\Property(property: 'birthDate', type: 'string', format: 'date', example: '2008-05-15', description: 'Player birth date (YYYY-MM-DD)')
            ]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Player created successfully',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 201),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'uuid', type: 'string', example: 'player-uuid'),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                        new OA\Property(property: 'fullName', type: 'string', example: 'John Doe'),
                        new OA\Property(property: 'position', type: 'string', example: 'Forward'),
                        new OA\Property(property: 'jerseyNumber', type: 'integer', example: 10),
                        new OA\Property(property: 'category', type: 'string', example: 'U16', description: 'Inherited from team category'),
                        new OA\Property(property: 'birthDate', type: 'string', example: '2008-05-15'),
                        new OA\Property(
                            property: 'team',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'team-uuid'),
                                new OA\Property(property: 'name', type: 'string', example: 'Lions U16')
                            ]
                        ),
                        new OA\Property(property: 'totalGoals', type: 'integer', example: 0),
                        new OA\Property(property: 'totalYellowCards', type: 'integer', example: 0),
                        new OA\Property(property: 'totalRedCards', type: 'integer', example: 0),
                        new OA\Property(property: 'totalAssists', type: 'integer', example: 0),
                        new OA\Property(property: 'totalMatches', type: 'integer', example: 0),
                        new OA\Property(property: 'status', type: 'string', example: 'IRREGULAR'),
                        new OA\Property(property: 'active', type: 'boolean', example: true),
                        new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Validation error - Duplicate name or invalid data'
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin or company owner privileges required'
    )]
    #[OA\Response(
        response: 404,
        description: 'Team not found or access denied'
    )]
    #[Security(name: 'Bearer')]
    public function createPlayer(Request $request)
    {
        return $this->result($this->service->createPlayer($request));
    }

    /**
     * Get all players
     */
    #[Rest\Get('/list')]
    #[OA\Get(
        path: '/api/v2/players/list',
        summary: 'Get all players',
        description: 'Retrieves all players with optional filtering',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'teamId',
        in: 'query',
        description: 'Filter by team ID',
        required: false,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Parameter(
        name: 'active',
        in: 'query',
        description: 'Filter by active status',
        required: false,
        schema: new OA\Schema(type: 'boolean')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of players',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 200),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'uuid', type: 'string', example: 'player-uuid'),
                            new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                            new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                            new OA\Property(property: 'fullName', type: 'string', example: 'John Doe'),
                            new OA\Property(property: 'position', type: 'string', example: 'Forward'),
                            new OA\Property(property: 'jerseyNumber', type: 'integer', example: 10),
                            new OA\Property(property: 'category', type: 'string', example: 'U16'),
                            new OA\Property(property: 'birthDate', type: 'string', example: '2008-05-15'),
                            new OA\Property(
                                property: 'team',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'uuid', type: 'string', example: 'team-uuid'),
                                    new OA\Property(property: 'name', type: 'string', example: 'Lions U16')
                                ]
                            ),
                            new OA\Property(property: 'totalGoals', type: 'integer', example: 5),
                            new OA\Property(property: 'totalYellowCards', type: 'integer', example: 2),
                            new OA\Property(property: 'totalRedCards', type: 'integer', example: 0),
                            new OA\Property(property: 'totalAssists', type: 'integer', example: 3),
                            new OA\Property(property: 'totalMatches', type: 'integer', example: 12),
                            new OA\Property(property: 'status', type: 'string', example: 'REGULAR'),
                            new OA\Property(property: 'active', type: 'boolean', example: true)
                        ]
                    )
                )
            ]
        )
    )]
    public function getAllPlayers(Request $request)
    {
        return $this->result($this->service->getAllPlayers($request));
    }

    /**
     * Get player by ID
     */
    #[Rest\Get('/show/{id}')]
    #[OA\Get(
        path: '/api/v2/players/show/{id}',
        summary: 'Get player by ID',
        description: 'Retrieves a player by ID',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Player ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Player details',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 200),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'uuid', type: 'string', example: 'player-uuid'),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                        new OA\Property(property: 'fullName', type: 'string', example: 'John Doe'),
                        new OA\Property(property: 'position', type: 'string', example: 'Forward'),
                        new OA\Property(property: 'jerseyNumber', type: 'integer', example: 10),
                        new OA\Property(property: 'category', type: 'string', example: 'U16'),
                        new OA\Property(property: 'birthDate', type: 'string', example: '2008-05-15'),
                        new OA\Property(
                            property: 'team',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'team-uuid'),
                                new OA\Property(property: 'name', type: 'string', example: 'Lions U16'),
                                new OA\Property(
                                    property: 'category',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'name', type: 'string', example: 'U16'),
                                        new OA\Property(property: 'code', type: 'string', example: 'U16')
                                    ]
                                )
                            ]
                        ),
                        new OA\Property(
                            property: 'statistics',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'totalGoals', type: 'integer', example: 5),
                                new OA\Property(property: 'totalYellowCards', type: 'integer', example: 2),
                                new OA\Property(property: 'totalRedCards', type: 'integer', example: 0),
                                new OA\Property(property: 'totalAssists', type: 'integer', example: 3),
                                new OA\Property(property: 'totalMatches', type: 'integer', example: 12),
                                new OA\Property(property: 'averageGoalsPerMatch', type: 'number', format: 'float', example: 0.42)
                            ]
                        ),
                        new OA\Property(property: 'status', type: 'string', example: 'REGULAR'),
                        new OA\Property(property: 'active', type: 'boolean', example: true),
                        new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(response: 404, description: 'Player not found')]
    public function getPlayer(string $id)
    {
        return $this->result($this->service->getPlayer($id));
    }

    /**
     * Update player
     */
    #[Rest\Put('/update/{id}')]
    #[OA\Put(
        path: '/api/v2/players/update/{id}',
        summary: 'Update player',
        description: 'Updates a player\'s information',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Player ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        description: 'Player data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'position', type: 'string'),
                new OA\Property(property: 'jerseyNumber', type: 'integer'),
                new OA\Property(property: 'teamId', type: 'integer'),
                new OA\Property(property: 'active', type: 'boolean')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Player updated successfully'
    )]
    public function updatePlayer(Request $request, string $id)
    {
        return $this->result($this->service->updatePlayer($request, $id));
    }

    /**
     * Record player statistics
     */
    #[Rest\Post('/statistics/create')]
    #[OA\Post(
        path: '/api/v2/players/statistics/create',
        summary: 'Record player statistics',
        description: 'Records statistics for a player in a match',
        security: [['Bearer' => []]]
    )]
    #[OA\RequestBody(
        description: 'Statistics data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'playerId', type: 'string'),
                new OA\Property(property: 'matchId', type: 'string'),
                new OA\Property(property: 'matchName', type: 'string'),
                new OA\Property(property: 'matchDate', type: 'string', format: 'date-time'),
                new OA\Property(property: 'goals', type: 'integer'),
                new OA\Property(property: 'yellowCards', type: 'integer'),
                new OA\Property(property: 'redCards', type: 'integer'),
                new OA\Property(property: 'assists', type: 'integer'),
                new OA\Property(property: 'notes', type: 'string')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Statistics recorded successfully'
    )]
    public function recordStatistics(Request $request)
    {
        return $this->result($this->service->recordStatistics($request));
    }

    /**
     * Get player statistics
     */
    #[Rest\Get('/statistics/show/{id}')]
    #[OA\Get(
        path: '/api/v2/players/statistics/show/{id}',
        summary: 'Get player statistics',
        description: 'Retrieves statistics for a player',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Player ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Player statistics'
    )]
    public function getPlayerStatistics(string $id)
    {
        return $this->result($this->service->getPlayerStatistics($id));
    }

    /**
     * Get top scorers
     */
    #[Rest\Get('/statistics/top-scorers')]
    #[OA\Get(
        path: '/api/v2/players/statistics/top-scorers',
        summary: 'Get top scorers',
        description: 'Retrieves top scoring players',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'limit',
        in: 'query',
        description: 'Number of players to return',
        required: false,
        schema: new OA\Schema(type: 'integer', default: 10)
    )]
    #[OA\Response(
        response: 200,
        description: 'List of top scorers'
    )]
    public function getTopScorers(Request $request)
    {
        $limit = $request->query->getInt('limit', 10);
        return $this->result($this->service->getTopScorers($limit));
    }

    /**
     * Get team statistics
     */
    #[Rest\Get('/statistics/team/{teamId}')]
    #[OA\Get(
        path: '/api/v2/players/statistics/team/{teamId}',
        summary: 'Get team statistics',
        description: 'Retrieves statistics for a team',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'teamId',
        in: 'path',
        description: 'Team ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Team statistics'
    )]
    public function getTeamStatistics(string $teamId)
    {
        return $this->result($this->service->getTeamStatistics($teamId));
    }

    /**
     * Delete player
     */
    #[Rest\Delete('/delete/{id}')]
    #[OA\Delete(
        path: '/api/v2/players/delete/{id}',
        summary: 'Delete player',
        description: 'Deletes a player and their associated data',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Player ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Player deleted successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'code', type: 'integer'),
                new OA\Property(property: 'status', type: 'string'),
                new OA\Property(property: 'message', type: 'string')
            ]
        )
    )]
    #[Security(name: 'Bearer')]
    public function deletePlayer(string $id)
    {
        return $this->result($this->service->deletePlayer($id));
    }
}