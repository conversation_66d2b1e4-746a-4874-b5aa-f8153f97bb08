<?php

namespace App\Entity\Tournament;

use App\Entity\Team\Team;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Tournament\EncounterRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: EncounterRepository::class)]
#[ORM\Table(name: 'encounters')]
class Encounter
{
    use IDableTrait;
    use UuidableTrait;
    use TimestampableTrait;
    use DeActivatableTrait;

    #[ORM\ManyToOne(targetEntity: Team::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?Team $homeTeam = null;

    #[ORM\ManyToOne(targetEntity: Team::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?Team $awayTeam = null;

    #[ORM\Column(type: Types::INTEGER)]
    private int $homeScore = 0;

    #[ORM\Column(type: Types::INTEGER)]
    private int $awayScore = 0;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $encounterDate = null;

    #[ORM\Column(length: 255)]
    private ?string $category = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $location = null;

    #[ORM\Column(length: 50)]
    private string $status = 'SCHEDULED';

    #[ORM\ManyToOne(targetEntity: TournamentDay::class, inversedBy: 'encounters')]
    #[ORM\JoinColumn(nullable: false)]
    private ?TournamentDay $tournamentDay = null;

    #[ORM\OneToMany(mappedBy: 'encounter', targetEntity: EncounterEvent::class, cascade: ['persist', 'remove'])]
    private Collection $events;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $notes = null;

    public function __construct()
    {
        $this->events = new ArrayCollection();
    }

    public function getHomeTeam(): ?Team
    {
        return $this->homeTeam;
    }

    public function setHomeTeam(?Team $homeTeam): static
    {
        $this->homeTeam = $homeTeam;
        return $this;
    }

    public function getAwayTeam(): ?Team
    {
        return $this->awayTeam;
    }

    public function setAwayTeam(?Team $awayTeam): static
    {
        $this->awayTeam = $awayTeam;
        return $this;
    }

    public function getHomeScore(): int
    {
        return $this->homeScore;
    }

    public function setHomeScore(int $homeScore): static
    {
        $this->homeScore = $homeScore;
        return $this;
    }

    public function getAwayScore(): int
    {
        return $this->awayScore;
    }

    public function setAwayScore(int $awayScore): static
    {
        $this->awayScore = $awayScore;
        return $this;
    }

    public function getEncounterDate(): ?\DateTimeInterface
    {
        return $this->encounterDate;
    }

    public function setEncounterDate(\DateTimeInterface $encounterDate): static
    {
        $this->encounterDate = $encounterDate;
        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(string $category): static
    {
        $this->category = $category;
        return $this;
    }

    public function getLocation(): ?string
    {
        return $this->location;
    }

    public function setLocation(?string $location): static
    {
        $this->location = $location;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;
        return $this;
    }

    public function getTournament(): ?Tournament
    {
        return $this->tournamentDay?->getTournament();
    }

    public function setTournament(?Tournament $tournament): static
    {
        // This method is kept for backward compatibility but doesn't do anything
        // The tournament relationship is now managed through TournamentDay
        return $this;
    }

    public function getTournamentDay(): ?TournamentDay
    {
        return $this->tournamentDay;
    }

    public function setTournamentDay(?TournamentDay $tournamentDay): static
    {
        $this->tournamentDay = $tournamentDay;
        return $this;
    }

    /**
     * @return Collection<int, EncounterEvent>
     */
    public function getEvents(): Collection
    {
        return $this->events;
    }

    public function addEvent(EncounterEvent $event): static
    {
        if (!$this->events->contains($event)) {
            $this->events->add($event);
            $event->setEncounter($this);
        }
        return $this;
    }

    public function removeEvent(EncounterEvent $event): static
    {
        if ($this->events->removeElement($event)) {
            if ($event->getEncounter() === $this) {
                $event->setEncounter(null);
            }
        }
        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): static
    {
        $this->notes = $notes;
        return $this;
    }

    public function getGoalEvents(): Collection
    {
        return $this->events->filter(fn(EncounterEvent $event) => $event->getType() === 'GOAL');
    }

    public function getCardEvents(): Collection
    {
        return $this->events->filter(fn(EncounterEvent $event) => in_array($event->getType(), ['YELLOW_CARD', 'RED_CARD']));
    }
}
