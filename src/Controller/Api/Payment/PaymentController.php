<?php

namespace App\Controller\Api\Payment;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Request;
use App\Controller\Api\Shared\AbstractBaseApiController;
use FOS\RestBundle\Controller\Annotations as Rest;
use App\Service\Payment\Implementation\PaymentService;

#[OA\Tag(name: 'Payments')]
class PaymentController extends AbstractBaseApiController
{
  private PaymentService $service;

  public function __construct(PaymentService $service)  
  {
    $this->service = $service;
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/payments/create/paid", name: "api_activities_payments_create")]
  public function createActivityPayment(Request $request)
  {
    $payUrl = $this->container->get('request_stack')->getCurrentRequest()->getSchemeAndHttpHost() . "/public/payments/";
    $data = $this->service->createActivityPayment($request, $payUrl);
    return $this->result($data);
  }



  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/payments/create/free", name: "api_commons_activities_payments_create_free")]
  public function createFreeActivityPayment(Request $request)
  {
    $data = $this->service->createFreeActivityPayment($request);
    return $this->result($data);
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */

  #[Rest\Get("/payments/activities/list", name: "api_commons_activities_payments_list")]
  public function getActivitiesPayments(Request $request)
  {
    $data = $this->service->getActivitiesPayments($request);
    return $this->result($data);
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
   #[Rest\Get("/payments/activities/pro/list", name: "api_commons_activities_payments_pro_list")]
   public function getActivitiesPaymentsByCompany(Request $request)
   {
     $data = $this->service->getActivitiesPaymentsByCompany($request);
     return $this->result($data);
   }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */

   #[Rest\Get("/admin/kpis/paiementsEvolutionAmount", name: "api_payments_evolution_amount")]
   public function getSuccessfulPaymentsByMethod(Request $request)
   {
     $data = $this->service->getSuccessfulPaymentsByMethod($request);
     return $this->result($data);
   }
  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/payments/get/{uuid}", name: "api_commons_activities_payments_findone")]
  public function getPayment(mixed $uuid)
  {
    $data = $this->service->getPayment($uuid);
    return $this->result($data);
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Delete("/payments/remove/{uuid}", name: "api_commons_activities_payments_removeone")]
  public function removePayment(mixed $uuid)
  {
    $data = $this->service->removePayment($uuid);
    return $this->result($data);
  }


  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/payments/{uuid}", name: "api_commons_activities_payments_findone_public")]
  public function getPaymentPublic(mixed $uuid)
  {
    $data = $this->service->getPayment($uuid);
    return $this->result($data);
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/public/payments/process", name: "api_commons_activities_payments_process")]
  public function processPaymentAction(Request $request)
  {
    $data = $this->service->processPayment($request);
    return $this->result($data);  
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/payments/check/{id}", name: "api_commons_activities_payments_check")]
  public function checkPaymentAction(int $id)
  {

    $data = $this->service->checkPayment($id);
    return $this->result($data);
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/payments/activities/check", name: "api_payments_activities_check")]
  public function checkPayReservation()
  {
    $data = $this->service->checkPayReservation();
    return $this->result($data);
  }
}