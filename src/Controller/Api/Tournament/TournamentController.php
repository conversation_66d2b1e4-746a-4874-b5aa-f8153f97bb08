<?php

namespace App\Controller\Api\Tournament;

use App\Service\Tournament\Implementation\TournamentService;
use App\Controller\Api\Shared\AbstractBaseApiController;
use Symfony\Component\HttpFoundation\Request;
use FOS\RestBundle\Controller\Annotations as Rest;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Security;

#[Rest\Route('/tournaments')]
#[OA\Tag(name: 'Tournaments')]
class TournamentController extends AbstractBaseApiController
{
    private TournamentService $service;

    public function __construct(TournamentService $service)
    {
        $this->service = $service;
    }

    /**
     * Create a new tournament (admin only)
     */
    #[Rest\Post('/create')]
    #[OA\Post(
        path: '/api/v2/tournaments/create',
        summary: 'Create a new tournament',
        description: 'Creates a new tournament (admin only)'
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            type: "object",
            required: ["name", "categoryId", "startDate", "endDate"],
            properties: [
                new OA\Property(property: 'name', type: 'string', example: 'Summer Championship 2024'),
                new OA\Property(property: 'description', type: 'string', example: 'Annual summer football tournament for youth teams'),
                new OA\Property(property: 'categoryId', type: 'string', example: 'uuid-of-team-category', description: 'UUID of the team category'),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2024-06-01'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2024-06-30'),
                new OA\Property(property: 'location', type: 'string', example: 'Main Stadium Complex'),
                new OA\Property(property: 'rules', type: 'string', example: 'Standard FIFA rules apply. 90 minutes per match.'),
                new OA\Property(property: 'maxTeams', type: 'integer', example: 16),
                new OA\Property(property: 'status', type: 'string', enum: ['DRAFT', 'ACTIVE', 'COMPLETED', 'CANCELLED'], example: 'DRAFT')
            ]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Tournament created successfully',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 201),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'tournament',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'uuid-string'),
                                new OA\Property(property: 'name', type: 'string', example: 'Summer Championship 2024'),
                                new OA\Property(property: 'description', type: 'string', example: 'Annual summer football tournament'),
                                new OA\Property(
                                    property: 'category',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 1),
                                        new OA\Property(property: 'uuid', type: 'string', example: 'category-uuid'),
                                        new OA\Property(property: 'name', type: 'string', example: 'U16'),
                                        new OA\Property(property: 'code', type: 'string', example: 'U16'),
                                        new OA\Property(property: 'ageRange', type: 'string', example: 'Under 15 years'),
                                        new OA\Property(property: 'gender', type: 'string', example: 'MIXED')
                                    ]
                                ),
                                new OA\Property(property: 'startDate', type: 'string', example: '2024-06-01'),
                                new OA\Property(property: 'endDate', type: 'string', example: '2024-06-30'),
                                new OA\Property(property: 'location', type: 'string', example: 'Main Stadium Complex'),
                                new OA\Property(property: 'status', type: 'string', example: 'DRAFT'),
                                new OA\Property(property: 'maxTeams', type: 'integer', example: 16),
                                new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                            ]
                        )
                    ]
                ),
                new OA\Property(
                    property: 'messages',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'message', type: 'string', example: 'Tournament created successfully'),
                            new OA\Property(property: 'code', type: 'string', example: 'tournament_created'),
                            new OA\Property(property: 'type', type: 'string', example: 'SUCCESS')
                        ]
                    )
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Validation error',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 400),
                new OA\Property(property: 'status', type: 'string', example: 'failed'),
                new OA\Property(
                    property: 'messages',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'message', type: 'string', example: 'Category ID is required.'),
                            new OA\Property(property: 'code', type: 'string', example: 'validation_error'),
                            new OA\Property(property: 'type', type: 'string', example: 'ERROR')
                        ]
                    )
                )
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin privileges required'
    )]
    #[OA\Response(
        response: 404,
        description: 'Team category not found'
    )]
    #[Security(name: 'Bearer')]
    public function createTournament(Request $request)
    {
        return $this->result($this->service->createTournament($request));
    }

    /**
     * Get all tournaments
     */
    #[Rest\Get('/list')]
    #[OA\Get(
        path: '/api/v2/tournaments/list',
        summary: 'Get all tournaments',
        description: 'Retrieves all tournaments with optional filtering',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'status',
        in: 'query',
        description: 'Filter by status',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'categoryId',
        in: 'query',
        description: 'Filter by team category UUID',
        required: false,
        schema: new OA\Schema(type: 'string'),
        example: 'uuid-of-team-category'
    )]
    #[OA\Parameter(
        name: 'active',
        in: 'query',
        description: 'Filter by active status',
        required: false,
        schema: new OA\Schema(type: 'boolean')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of tournaments',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 200),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'uuid', type: 'string', example: 'tournament-uuid'),
                            new OA\Property(property: 'name', type: 'string', example: 'Summer Championship 2024'),
                            new OA\Property(property: 'description', type: 'string', example: 'Annual summer tournament'),
                            new OA\Property(
                                property: 'category',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'uuid', type: 'string', example: 'category-uuid'),
                                    new OA\Property(property: 'name', type: 'string', example: 'U16'),
                                    new OA\Property(property: 'code', type: 'string', example: 'U16'),
                                    new OA\Property(property: 'ageRange', type: 'string', example: 'Under 15 years'),
                                    new OA\Property(property: 'gender', type: 'string', example: 'MIXED')
                                ]
                            ),
                            new OA\Property(property: 'startDate', type: 'string', example: '2024-06-01'),
                            new OA\Property(property: 'endDate', type: 'string', example: '2024-06-30'),
                            new OA\Property(property: 'location', type: 'string', example: 'Main Stadium Complex'),
                            new OA\Property(property: 'status', type: 'string', example: 'ACTIVE'),
                            new OA\Property(property: 'maxTeams', type: 'integer', example: 16),
                            new OA\Property(property: 'teamsCount', type: 'integer', example: 12),
                            new OA\Property(property: 'encountersCount', type: 'integer', example: 8),
                            new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                        ]
                    )
                )
            ]
        )
    )]
    public function getAllTournaments(Request $request)
    {
        return $this->result($this->service->getAllTournaments($request));
    }

    /**
     * Get tournament by ID
     */
    #[Rest\Get('/show/{id}')]
    #[OA\Get(
        path: '/api/v2/tournaments/show/{id}',
        summary: 'Get tournament by ID',
        description: 'Retrieves a tournament by ID',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament details',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 200),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'uuid', type: 'string', example: 'tournament-uuid'),
                        new OA\Property(property: 'name', type: 'string', example: 'Summer Championship 2024'),
                        new OA\Property(property: 'description', type: 'string', example: 'Annual summer tournament'),
                        new OA\Property(
                            property: 'category',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'category-uuid'),
                                new OA\Property(property: 'name', type: 'string', example: 'U16'),
                                new OA\Property(property: 'code', type: 'string', example: 'U16'),
                                new OA\Property(property: 'ageRange', type: 'string', example: 'Under 15 years'),
                                new OA\Property(property: 'gender', type: 'string', example: 'MIXED')
                            ]
                        ),
                        new OA\Property(property: 'startDate', type: 'string', example: '2024-06-01'),
                        new OA\Property(property: 'endDate', type: 'string', example: '2024-06-30'),
                        new OA\Property(property: 'location', type: 'string', example: 'Main Stadium Complex'),
                        new OA\Property(property: 'rules', type: 'string', example: 'Standard FIFA rules apply'),
                        new OA\Property(property: 'status', type: 'string', example: 'ACTIVE'),
                        new OA\Property(property: 'maxTeams', type: 'integer', example: 16),
                        new OA\Property(
                            property: 'teams',
                            type: 'array',
                            items: new OA\Items(
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'uuid', type: 'string', example: 'team-uuid'),
                                    new OA\Property(property: 'name', type: 'string', example: 'Lions U16'),
                                    new OA\Property(property: 'playersCount', type: 'integer', example: 18)
                                ]
                            )
                        ),
                        new OA\Property(
                            property: 'encounters',
                            type: 'array',
                            items: new OA\Items(
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'uuid', type: 'string', example: 'encounter-uuid'),
                                    new OA\Property(property: 'homeTeam', type: 'string', example: 'Lions U16'),
                                    new OA\Property(property: 'awayTeam', type: 'string', example: 'Eagles U16'),
                                    new OA\Property(property: 'homeScore', type: 'integer', example: 2),
                                    new OA\Property(property: 'awayScore', type: 'integer', example: 1),
                                    new OA\Property(property: 'status', type: 'string', example: 'COMPLETED'),
                                    new OA\Property(property: 'encounterDate', type: 'string', example: '2024-06-15 15:00:00')
                                ]
                            )
                        ),
                        new OA\Property(property: 'teamsCount', type: 'integer', example: 12),
                        new OA\Property(property: 'encountersCount', type: 'integer', example: 8),
                        new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(response: 404, description: 'Tournament not found')]
    public function getTournament(string $id)
    {
        return $this->result($this->service->getTournament($id));
    }

    /**
     * Update tournament (admin only)
     */
    #[Rest\Put('/update/{id}')]
    #[OA\Put(
        path: '/api/v2/tournaments/update/{id}',
        summary: 'Update tournament',
        description: 'Updates a tournament (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        description: 'Tournament data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'name', type: 'string'),
                new OA\Property(property: 'description', type: 'string'),
                new OA\Property(property: 'category', type: 'string'),
                new OA\Property(property: 'startDate', type: 'string', format: 'date'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date'),
                new OA\Property(property: 'location', type: 'string'),
                new OA\Property(property: 'rules', type: 'string'),
                new OA\Property(property: 'maxTeams', type: 'integer'),
                new OA\Property(property: 'status', type: 'string')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament updated successfully'
    )]
    public function updateTournament(Request $request, string $id)
    {
        return $this->result($this->service->updateTournament($request, $id));
    }

    /**
     * Delete tournament (admin only)
     */
    #[Rest\Delete('/delete/{id}')]
    #[OA\Delete(
        path: '/api/v2/tournaments/delete/{id}',
        summary: 'Delete tournament',
        description: 'Deletes a tournament (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament deleted successfully'
    )]
    public function deleteTournament(string $id)
    {
        return $this->result($this->service->deleteTournament($id));
    }

    /**
     * Add company to tournament (admin only)
     */
    #[Rest\Post('/add-company')]
    #[OA\Post(
        path: '/api/v2/tournaments/add-company',
        summary: 'Add company to tournament',
        description: 'Adds a company to a tournament (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(properties: [
            new OA\Property(property: 'tournamentId', type: 'string'),
            new OA\Property(property: 'companyId', type: 'string')
        ])
    )]
    #[OA\Response(
        response: 200,
        description: 'Company added to tournament successfully'
    )]
    public function addCompanyToTournament(Request $request)
    {
        return $this->result($this->service->addCompanyToTournament($request));
    }

    /**
     * Remove company from tournament (admin only)
     */
    #[Rest\Post('/remove-company')]
    #[OA\Post(
        path: '/api/v2/tournaments/remove-company',
        summary: 'Remove company from tournament',
        description: 'Removes a company from a tournament (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(properties: [
            new OA\Property(property: 'tournamentId', type: 'string'),
            new OA\Property(property: 'companyId', type: 'string')
        ])
    )]
    #[OA\Response(
        response: 200,
        description: 'Company removed from tournament successfully'
    )]
    public function removeCompanyFromTournament(Request $request)
    {
        return $this->result($this->service->removeCompanyFromTournament($request));
    }

    /**
     * Get tournament statistics
     */
    #[Rest\Get('/statistics/{id}')]
    #[OA\Get(
        path: '/api/v2/tournaments/statistics/{id}',
        summary: 'Get tournament statistics',
        description: 'Retrieves statistics for a tournament',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament statistics'
    )]
    public function getTournamentStatistics(string $id)
    {
        return $this->result($this->service->getTournamentStatistics($id));
    }

    /**
     * Get tournament standings
     */
    #[Rest\Get('/standings/{id}')]
    #[OA\Get(
        path: '/api/v2/tournaments/standings/{id}',
        summary: 'Get tournament standings',
        description: 'Retrieves standings/rankings for a tournament',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament standings'
    )]
    public function getTournamentStandings(string $id)
    {
        return $this->result($this->service->getTournamentStandings($id));
    }
}
