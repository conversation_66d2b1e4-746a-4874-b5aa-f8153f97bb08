<?php

namespace App\Repository\Tournament;

use App\Entity\Tournament\Tournament;
use App\Entity\Tournament\TournamentDay;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TournamentDay>
 *
 * @method TournamentDay|null find($id, $lockMode = null, $lockVersion = null)
 * @method TournamentDay|null findOneBy(array $criteria, array $orderBy = null)
 * @method TournamentDay[]    findAll()
 * @method TournamentDay[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TournamentDayRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TournamentDay::class);
    }

    /**
     * Find tournament day by UUID
     */
    public function getByUuid(string $uuid): ?TournamentDay
    {
        return $this->findOneBy(['uuid' => $uuid, 'active' => true]);
    }

    /**
     * Find tournament day by UUID (alias for consistency)
     */
    public function findByUuid(string $uuid): ?TournamentDay
    {
        return $this->getByUuid($uuid);
    }

    /**
     * Find all tournament days for a specific tournament
     */
    public function findByTournament(Tournament $tournament): array
    {
        return $this->createQueryBuilder('td')
            ->where('td.tournament = :tournament')
            ->andWhere('td.active = :active')
            ->setParameter('tournament', $tournament)
            ->setParameter('active', true)
            ->orderBy('td.dayNumber', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tournament days by date range
     */
    public function findByDateRange(\DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        return $this->createQueryBuilder('td')
            ->where('td.date BETWEEN :startDate AND :endDate')
            ->andWhere('td.active = :active')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('active', true)
            ->orderBy('td.date', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tournament days for today
     */
    public function findForToday(): array
    {
        $today = new \DateTime('today');
        
        return $this->createQueryBuilder('td')
            ->where('td.date = :today')
            ->andWhere('td.active = :active')
            ->setParameter('today', $today)
            ->setParameter('active', true)
            ->orderBy('td.dayNumber', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find upcoming tournament days
     */
    public function findUpcoming(int $limit = 10): array
    {
        $today = new \DateTime('today');
        
        return $this->createQueryBuilder('td')
            ->where('td.date >= :today')
            ->andWhere('td.active = :active')
            ->setParameter('today', $today)
            ->setParameter('active', true)
            ->orderBy('td.date', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tournament days with encounters count
     */
    public function findWithEncountersCount(Tournament $tournament): array
    {
        return $this->createQueryBuilder('td')
            ->select('td', 'COUNT(e.id) as encountersCount')
            ->leftJoin('td.encounters', 'e')
            ->where('td.tournament = :tournament')
            ->andWhere('td.active = :active')
            ->setParameter('tournament', $tournament)
            ->setParameter('active', true)
            ->groupBy('td.id')
            ->orderBy('td.dayNumber', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Check if day number exists for tournament
     */
    public function dayNumberExists(Tournament $tournament, int $dayNumber, ?int $excludeId = null): bool
    {
        $qb = $this->createQueryBuilder('td')
            ->select('COUNT(td.id)')
            ->where('td.tournament = :tournament')
            ->andWhere('td.dayNumber = :dayNumber')
            ->andWhere('td.active = :active')
            ->setParameter('tournament', $tournament)
            ->setParameter('dayNumber', $dayNumber)
            ->setParameter('active', true);

        if ($excludeId) {
            $qb->andWhere('td.id != :excludeId')
               ->setParameter('excludeId', $excludeId);
        }

        return $qb->getQuery()->getSingleScalarResult() > 0;
    }

    /**
     * Check if date exists for tournament
     */
    public function dateExists(Tournament $tournament, \DateTimeInterface $date, ?int $excludeId = null): bool
    {
        $qb = $this->createQueryBuilder('td')
            ->select('COUNT(td.id)')
            ->where('td.tournament = :tournament')
            ->andWhere('td.date = :date')
            ->andWhere('td.active = :active')
            ->setParameter('tournament', $tournament)
            ->setParameter('date', $date)
            ->setParameter('active', true);

        if ($excludeId) {
            $qb->andWhere('td.id != :excludeId')
               ->setParameter('excludeId', $excludeId);
        }

        return $qb->getQuery()->getSingleScalarResult() > 0;
    }

    /**
     * Get next day number for tournament
     */
    public function getNextDayNumber(Tournament $tournament): int
    {
        $result = $this->createQueryBuilder('td')
            ->select('MAX(td.dayNumber)')
            ->where('td.tournament = :tournament')
            ->andWhere('td.active = :active')
            ->setParameter('tournament', $tournament)
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();

        return ($result ?? 0) + 1;
    }

    /**
     * Find tournament days with statistics
     */
    public function findWithStatistics(Tournament $tournament): array
    {
        return $this->createQueryBuilder('td')
            ->select('td', 
                'COUNT(e.id) as totalEncounters',
                'COUNT(CASE WHEN e.status = \'COMPLETED\' THEN 1 END) as completedEncounters',
                'COUNT(CASE WHEN e.status = \'SCHEDULED\' THEN 1 END) as scheduledEncounters',
                'COUNT(CASE WHEN e.status = \'LIVE\' THEN 1 END) as liveEncounters'
            )
            ->leftJoin('td.encounters', 'e')
            ->where('td.tournament = :tournament')
            ->andWhere('td.active = :active')
            ->setParameter('tournament', $tournament)
            ->setParameter('active', true)
            ->groupBy('td.id')
            ->orderBy('td.dayNumber', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Save tournament day
     */
    public function save(TournamentDay $tournamentDay, bool $flush = false): void
    {
        $this->getEntityManager()->persist($tournamentDay);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Remove tournament day
     */
    public function remove(TournamentDay $tournamentDay, bool $flush = false): void
    {
        $this->getEntityManager()->remove($tournamentDay);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
