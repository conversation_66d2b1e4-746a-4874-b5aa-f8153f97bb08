<?php

namespace App\Entity\Tournament;

use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Tournament\TournamentDayRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: TournamentDayRepository::class)]
#[ORM\Table(name: 'tournament_days')]
class TournamentDay
{
    use IDableTrait;
    use UuidableTrait;
    use DeActivatableTrait;
    use TimestampableTrait;

    #[ORM\ManyToOne(targetEntity: Tournament::class, inversedBy: 'tournamentDays')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Tournament $tournament = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $date = null;

    #[ORM\Column]
    private ?int $dayNumber = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $name = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\OneToMany(targetEntity: Encounter::class, mappedBy: 'tournamentDay', cascade: ['persist', 'remove'])]
    private Collection $encounters;

    public function __construct()
    {
        $this->encounters = new ArrayCollection();
        $this->active = true; // Set default active state
    }

    public function getTournament(): ?Tournament
    {
        return $this->tournament;
    }

    public function setTournament(?Tournament $tournament): static
    {
        $this->tournament = $tournament;
        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): static
    {
        $this->date = $date;
        return $this;
    }

    public function getDayNumber(): ?int
    {
        return $this->dayNumber;
    }

    public function setDayNumber(int $dayNumber): static
    {
        $this->dayNumber = $dayNumber;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): static
    {
        $this->name = $name;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        return $this;
    }

    /**
     * @return Collection<int, Encounter>
     */
    public function getEncounters(): Collection
    {
        return $this->encounters;
    }

    public function addEncounter(Encounter $encounter): static
    {
        if (!$this->encounters->contains($encounter)) {
            $this->encounters->add($encounter);
            $encounter->setTournamentDay($this);
        }

        return $this;
    }

    public function removeEncounter(Encounter $encounter): static
    {
        if ($this->encounters->removeElement($encounter)) {
            // set the owning side to null (unless already changed)
            if ($encounter->getTournamentDay() === $this) {
                $encounter->setTournamentDay(null);
            }
        }

        return $this;
    }

    /**
     * Get the number of encounters for this day
     */
    public function getEncountersCount(): int
    {
        return $this->encounters->count();
    }

    /**
     * Get completed encounters count
     */
    public function getCompletedEncountersCount(): int
    {
        return $this->encounters->filter(function(Encounter $encounter) {
            return $encounter->getStatus() === 'COMPLETED';
        })->count();
    }

    /**
     * Get scheduled encounters count
     */
    public function getScheduledEncountersCount(): int
    {
        return $this->encounters->filter(function(Encounter $encounter) {
            return $encounter->getStatus() === 'SCHEDULED';
        })->count();
    }

    /**
     * Check if this day is in the past
     */
    public function isPast(): bool
    {
        return $this->date < new \DateTime('today');
    }

    /**
     * Check if this day is today
     */
    public function isToday(): bool
    {
        return $this->date->format('Y-m-d') === (new \DateTime('today'))->format('Y-m-d');
    }

    /**
     * Check if this day is in the future
     */
    public function isFuture(): bool
    {
        return $this->date > new \DateTime('today');
    }

    /**
     * Get formatted date string
     */
    public function getFormattedDate(): string
    {
        return $this->date->format('Y-m-d');
    }

    /**
     * Get day status based on encounters
     */
    public function getDayStatus(): string
    {
        if ($this->getEncountersCount() === 0) {
            return 'NO_ENCOUNTERS';
        }

        if ($this->getCompletedEncountersCount() === $this->getEncountersCount()) {
            return 'COMPLETED';
        }

        if ($this->getCompletedEncountersCount() > 0) {
            return 'IN_PROGRESS';
        }

        if ($this->isPast()) {
            return 'MISSED';
        }

        if ($this->isToday()) {
            return 'TODAY';
        }

        return 'SCHEDULED';
    }
}
