<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250619165705 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE encounter_events (id INT AUTO_INCREMENT NOT NULL, encounter_id INT NOT NULL, player_id INT NOT NULL, assist_player_id INT DEFAULT NULL, type VARCHAR(50) NOT NULL, minute INT DEFAULT NULL, description LONGTEXT DEFAULT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', UNIQUE INDEX UNIQ_5761B042D17F50A6 (uuid), INDEX IDX_5761B042D6E2FADC (encounter_id), INDEX IDX_5761B04299E6F5DF (player_id), INDEX IDX_5761B0425904EF7F (assist_player_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE encounters (id INT AUTO_INCREMENT NOT NULL, home_team_id INT NOT NULL, away_team_id INT NOT NULL, tournament_day_id INT NOT NULL, home_score INT NOT NULL, away_score INT NOT NULL, encounter_date DATETIME NOT NULL, category VARCHAR(255) NOT NULL, location VARCHAR(255) DEFAULT NULL, status VARCHAR(50) NOT NULL, notes LONGTEXT DEFAULT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', active TINYINT(1) DEFAULT 0 NOT NULL, UNIQUE INDEX UNIQ_60D6368CD17F50A6 (uuid), INDEX IDX_60D6368C9C4C13F6 (home_team_id), INDEX IDX_60D6368C45185D02 (away_team_id), INDEX IDX_60D6368C6D68BF76 (tournament_day_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE player_statistics (id INT AUTO_INCREMENT NOT NULL, player_id INT NOT NULL, encounter_id VARCHAR(255) NOT NULL, encounter_name VARCHAR(255) NOT NULL, encounter_date DATETIME NOT NULL, goals INT DEFAULT NULL, yellow_cards INT DEFAULT NULL, red_cards INT DEFAULT NULL, assists INT DEFAULT NULL, notes LONGTEXT DEFAULT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', UNIQUE INDEX UNIQ_BD760F1FD17F50A6 (uuid), INDEX IDX_BD760F1F99E6F5DF (player_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE players (id INT AUTO_INCREMENT NOT NULL, team_id INT NOT NULL, first_name VARCHAR(255) DEFAULT NULL, last_name VARCHAR(255) DEFAULT NULL, category VARCHAR(255) DEFAULT NULL, birth_date DATE DEFAULT NULL, photo VARCHAR(255) DEFAULT NULL, idcard_recto VARCHAR(255) DEFAULT NULL, idcard_verso VARCHAR(255) DEFAULT NULL, position VARCHAR(255) NOT NULL, jersey_number INT DEFAULT NULL, total_goals INT DEFAULT NULL, total_yellow_cards INT DEFAULT NULL, total_red_cards INT DEFAULT NULL, total_assists INT DEFAULT NULL, total_matches INT DEFAULT NULL, status VARCHAR(255) NOT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', active TINYINT(1) DEFAULT 0 NOT NULL, UNIQUE INDEX UNIQ_264E43A6D17F50A6 (uuid), INDEX IDX_264E43A6296CD8AE (team_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE team_categories (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, code VARCHAR(100) NOT NULL, min_age INT DEFAULT NULL, max_age INT DEFAULT NULL, max_players_per_team INT DEFAULT NULL, min_players_per_team INT DEFAULT NULL, gender VARCHAR(255) DEFAULT NULL, rules LONGTEXT DEFAULT NULL, sort_order INT NOT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', active TINYINT(1) DEFAULT 0 NOT NULL, description LONGTEXT DEFAULT NULL, UNIQUE INDEX UNIQ_42A34AD5E237E06 (name), UNIQUE INDEX UNIQ_42A34AD77153098 (code), UNIQUE INDEX UNIQ_42A34ADD17F50A6 (uuid), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE teams (id INT AUTO_INCREMENT NOT NULL, category_id INT NOT NULL, company_id INT NOT NULL, name VARCHAR(255) NOT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', active TINYINT(1) DEFAULT 0 NOT NULL, UNIQUE INDEX UNIQ_96C22258D17F50A6 (uuid), INDEX IDX_96C2225812469DE2 (category_id), INDEX IDX_96C22258979B1AD6 (company_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE tournament_days (id INT AUTO_INCREMENT NOT NULL, tournament_id INT NOT NULL, date DATE NOT NULL, day_number INT NOT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', active TINYINT(1) DEFAULT 0 NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', UNIQUE INDEX UNIQ_DC694E58D17F50A6 (uuid), INDEX IDX_DC694E5833D1A3E7 (tournament_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE tournaments (id INT AUTO_INCREMENT NOT NULL, category_id INT NOT NULL, name VARCHAR(255) NOT NULL, start_date DATE NOT NULL, end_date DATE NOT NULL, location VARCHAR(255) DEFAULT NULL, rules LONGTEXT DEFAULT NULL, max_teams INT DEFAULT NULL, status VARCHAR(50) NOT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', active TINYINT(1) DEFAULT 0 NOT NULL, description LONGTEXT DEFAULT NULL, UNIQUE INDEX UNIQ_E4BCFAC3D17F50A6 (uuid), INDEX IDX_E4BCFAC312469DE2 (category_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE tournament_companies (tournament_id INT NOT NULL, company_id INT NOT NULL, INDEX IDX_52C4022B33D1A3E7 (tournament_id), INDEX IDX_52C4022B979B1AD6 (company_id), PRIMARY KEY(tournament_id, company_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE encounter_events ADD CONSTRAINT FK_5761B042D6E2FADC FOREIGN KEY (encounter_id) REFERENCES encounters (id)');
        $this->addSql('ALTER TABLE encounter_events ADD CONSTRAINT FK_5761B04299E6F5DF FOREIGN KEY (player_id) REFERENCES players (id)');
        $this->addSql('ALTER TABLE encounter_events ADD CONSTRAINT FK_5761B0425904EF7F FOREIGN KEY (assist_player_id) REFERENCES players (id)');
        $this->addSql('ALTER TABLE encounters ADD CONSTRAINT FK_60D6368C9C4C13F6 FOREIGN KEY (home_team_id) REFERENCES teams (id)');
        $this->addSql('ALTER TABLE encounters ADD CONSTRAINT FK_60D6368C45185D02 FOREIGN KEY (away_team_id) REFERENCES teams (id)');
        $this->addSql('ALTER TABLE encounters ADD CONSTRAINT FK_60D6368C6D68BF76 FOREIGN KEY (tournament_day_id) REFERENCES tournament_days (id)');
        $this->addSql('ALTER TABLE player_statistics ADD CONSTRAINT FK_BD760F1F99E6F5DF FOREIGN KEY (player_id) REFERENCES players (id)');
        $this->addSql('ALTER TABLE players ADD CONSTRAINT FK_264E43A6296CD8AE FOREIGN KEY (team_id) REFERENCES teams (id)');
        $this->addSql('ALTER TABLE teams ADD CONSTRAINT FK_96C2225812469DE2 FOREIGN KEY (category_id) REFERENCES team_categories (id)');
        $this->addSql('ALTER TABLE teams ADD CONSTRAINT FK_96C22258979B1AD6 FOREIGN KEY (company_id) REFERENCES companies (id)');
        $this->addSql('ALTER TABLE tournament_days ADD CONSTRAINT FK_DC694E5833D1A3E7 FOREIGN KEY (tournament_id) REFERENCES tournaments (id)');
        $this->addSql('ALTER TABLE tournaments ADD CONSTRAINT FK_E4BCFAC312469DE2 FOREIGN KEY (category_id) REFERENCES team_categories (id)');
        $this->addSql('ALTER TABLE tournament_companies ADD CONSTRAINT FK_52C4022B33D1A3E7 FOREIGN KEY (tournament_id) REFERENCES tournaments (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE tournament_companies ADD CONSTRAINT FK_52C4022B979B1AD6 FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE licenses ADD player_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE licenses ADD CONSTRAINT FK_7F320F3F99E6F5DF FOREIGN KEY (player_id) REFERENCES players (id)');
        $this->addSql('CREATE INDEX IDX_7F320F3F99E6F5DF ON licenses (player_id)');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE licenses DROP FOREIGN KEY FK_7F320F3F99E6F5DF');
        $this->addSql('ALTER TABLE encounter_events DROP FOREIGN KEY FK_5761B042D6E2FADC');
        $this->addSql('ALTER TABLE encounter_events DROP FOREIGN KEY FK_5761B04299E6F5DF');
        $this->addSql('ALTER TABLE encounter_events DROP FOREIGN KEY FK_5761B0425904EF7F');
        $this->addSql('ALTER TABLE encounters DROP FOREIGN KEY FK_60D6368C9C4C13F6');
        $this->addSql('ALTER TABLE encounters DROP FOREIGN KEY FK_60D6368C45185D02');
        $this->addSql('ALTER TABLE encounters DROP FOREIGN KEY FK_60D6368C6D68BF76');
        $this->addSql('ALTER TABLE player_statistics DROP FOREIGN KEY FK_BD760F1F99E6F5DF');
        $this->addSql('ALTER TABLE players DROP FOREIGN KEY FK_264E43A6296CD8AE');
        $this->addSql('ALTER TABLE teams DROP FOREIGN KEY FK_96C2225812469DE2');
        $this->addSql('ALTER TABLE teams DROP FOREIGN KEY FK_96C22258979B1AD6');
        $this->addSql('ALTER TABLE tournament_days DROP FOREIGN KEY FK_DC694E5833D1A3E7');
        $this->addSql('ALTER TABLE tournaments DROP FOREIGN KEY FK_E4BCFAC312469DE2');
        $this->addSql('ALTER TABLE tournament_companies DROP FOREIGN KEY FK_52C4022B33D1A3E7');
        $this->addSql('ALTER TABLE tournament_companies DROP FOREIGN KEY FK_52C4022B979B1AD6');
        $this->addSql('DROP TABLE encounter_events');
        $this->addSql('DROP TABLE encounters');
        $this->addSql('DROP TABLE player_statistics');
        $this->addSql('DROP TABLE players');
        $this->addSql('DROP TABLE team_categories');
        $this->addSql('DROP TABLE teams');
        $this->addSql('DROP TABLE tournament_days');
        $this->addSql('DROP TABLE tournaments');
        $this->addSql('DROP TABLE tournament_companies');
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('DROP INDEX IDX_7F320F3F99E6F5DF ON licenses');
        $this->addSql('ALTER TABLE licenses DROP player_id');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
    }
}
