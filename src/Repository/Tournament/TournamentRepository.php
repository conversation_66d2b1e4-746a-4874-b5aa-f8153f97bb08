<?php

namespace App\Repository\Tournament;

use App\Entity\Tournament\Tournament;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Tournament>
 *
 * @method Tournament|null find($id, $lockMode = null, $lockVersion = null)
 * @method Tournament|null findOneBy(array $criteria, array $orderBy = null)
 * @method Tournament[]    findAll()
 * @method Tournament[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TournamentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Tournament::class);
    }

    public function save(Tournament $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Tournament $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find tournament by UUID
     */
    public function getByUuid(string $uuid): ?Tournament
    {
        return $this->findOneBy(['uuid' => $uuid, 'active' => true]);
    }

    /**
     * Find tournaments by status
     */
    public function findByStatus(string $status): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.status = :status')
            ->setParameter('status', $status)
            ->orderBy('t.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find active tournaments
     */
    public function findActiveTournaments(): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.status IN (:statuses)')
            ->setParameter('statuses', ['ACTIVE', 'ONGOING'])
            ->andWhere('t.active = :active')
            ->setParameter('active', true)
            ->orderBy('t.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tournaments by category
     */
    public function findByCategory(string $category): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.category = :category')
            ->setParameter('category', $category)
            ->andWhere('t.active = :active')
            ->setParameter('active', true)
            ->orderBy('t.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tournaments that a company is participating in
     */
    public function findByCompany(int $companyId): array
    {
        return $this->createQueryBuilder('t')
            ->join('t.companies', 'c')
            ->andWhere('c.id = :companyId')
            ->setParameter('companyId', $companyId)
            ->andWhere('t.active = :active')
            ->setParameter('active', true)
            ->orderBy('t.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
