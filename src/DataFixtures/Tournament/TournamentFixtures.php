<?php

namespace App\DataFixtures\Tournament;

use App\Entity\Tournament\Tournament;
use App\Entity\Tournament\TournamentDay;
use App\Entity\Tournament\Encounter;
use App\DataFixtures\Team\TeamFixtures;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\Uid\Uuid;

class TournamentFixtures extends Fixture implements DependentFixtureInterface
{
    private function findExistingTournament(ObjectManager $manager, string $name): ?Tournament
    {
        return $manager->getRepository(Tournament::class)->findOneBy(['name' => $name]);
    }


    private function findExistingTournamentDay(ObjectManager $manager, Tournament $tournament, int $dayNumber): ?TournamentDay
    {
        return $manager->getRepository(TournamentDay::class)->findOneBy([
            'tournament' => $tournament,
            'dayNumber' => $dayNumber
        ]);
    }

    private function findExistingEncounter(ObjectManager $manager, TournamentDay $tournamentDay, \DateTime $encounterDate): ?Encounter
    {
        return $manager->getRepository(Encounter::class)->findOneBy([
            'tournamentDay' => $tournamentDay,
            'encounterDate' => $encounterDate
        ]);
    }

    public function load(ObjectManager $manager): void
    {
        $categories = ['u13', 'u14', 'u15', 'u16', 'u17'];
        $companies = [
            '43e814c7-4af5-4484-97e9-7d20e8e97b46',
            'acfc2abb-3bf8-4a30-88db-1f5114443172',
            '2817d896-a60a-4704-a73d-bcebf817fd99',
            '508a3718-469b-40da-8079-6a54be1b0cd0',
            '836d8364-5b23-4fc8-a7a7-a12458c28a8e',
            'ee3c5fea-fd38-43cc-8ded-39639546a7bd',
            '01310020-ed5e-434c-b074-4ab5121a1fb8',
            'dd1dd5ab-bce7-4e4d-ac71-e74cb96dd2cd',
            '8cb6a894-044c-4daa-872c-c74230ce361f'
        ];

        foreach ($categories as $category) {
            $tournamentName = "Youth Championship 2025 - " . strtoupper($category);
            $tournament = $this->findExistingTournament($manager, $tournamentName);
            
            if (!$tournament) {
                $tournament = new Tournament();
                // UUID is only set for new tournaments
            }
            
            // Update tournament properties
            $tournament->setName("Youth Championship 2025 - " . strtoupper($category));
            $tournament->setCategory($this->getReference('category_' . $category));
            $tournament->setStartDate(new \DateTime('2025-07-01'));
            $tournament->setEndDate(new \DateTime('2025-07-31'));
            $tournament->setLocation('Stade Omnisports, Douala');
            $tournament->setMaxTeams(count($companies));
            $tournament->setStatus('SCHEDULED');
            $tournament->setDescription('Official Youth Championship Tournament 2025 for ' . strtoupper($category) . ' category');
            $tournament->setRules("1. Match duration: 2x45 minutes\n2. Maximum squad size: 23 players\n3. Minimum players to start: 7\n4. Substitutions allowed: 5\n5. Points system: Win=3, Draw=1, Loss=0");
            $tournament->setActive(true);
            $tournament->setUuid(Uuid::v4());
            
            // Add all companies to the tournament
            foreach ($companies as $companyUuid) {
                $tournament->addCompany($this->getReference('company_' . $companyUuid));
            }

            $manager->persist($tournament);

            // Create tournament days and encounters
            $dates = [
                '2025-07-05', '2025-07-12', '2025-07-19', '2025-07-26'
            ];

            foreach ($dates as $index => $date) {
                $tournamentDay = $this->findExistingTournamentDay($manager, $tournament, $index + 1);
                
                if (!$tournamentDay) {
                    $tournamentDay = new TournamentDay();
                    // UUID is only set for new tournament days
                }
                
                // Update tournament day properties
                $tournamentDay->setTournament($tournament);
                $tournamentDay->setDayNumber($index + 1);
                $tournamentDay->setDate(new \DateTime($date));
                $tournamentDay->setName("Matchday " . ($index + 1));
                $tournamentDay->setDescription("Tournament matches for " . strtoupper($category) . " category - Day " . ($index + 1));
                $tournamentDay->setActive(true);
                $tournamentDay->setUuid(Uuid::v4());

                $manager->persist($tournamentDay);

                // Create encounters for this day
                // We'll create 2 matches per day
                for ($i = 0; $i < 2; $i++) {
                    $homeTeamUuid = $companies[array_rand($companies)];
                    do {
                        $awayTeamUuid = $companies[array_rand($companies)];
                    } while ($homeTeamUuid === $awayTeamUuid);

                    $encounterDate = new \DateTime($date . ' ' . ($i == 0 ? '14:00:00' : '16:00:00'));
                    $encounter = $this->findExistingEncounter($manager, $tournamentDay, $encounterDate);
                    
                    if (!$encounter) {
                        $encounter = new Encounter();
                        // UUID is only set for new encounters
                    }
                    
                    // Update encounter properties
                    $encounter->setTournamentDay($tournamentDay);
                    $encounter->setHomeTeam($this->getReference('team_' . $homeTeamUuid . '_' . $category));
                    $encounter->setAwayTeam($this->getReference('team_' . $awayTeamUuid . '_' . $category));
                    $encounter->setStatus('SCHEDULED');
                    $encounter->setEncounterDate(new \DateTime($date . ' ' . ($i == 0 ? '14:00:00' : '16:00:00')));
                    $encounter->setCategory(strtoupper($category));
                    $encounter->setLocation('Stade Omnisports, Douala');
                    $encounter->setHomeScore(0);
                    $encounter->setAwayScore(0);
                    $encounter->setActive(true);
                    $encounter->setUuid(Uuid::v4());

                    $manager->persist($encounter);
                }
            }
        }

        $manager->flush();
    }

    public function getDependencies(): array
    {
        return [
            TeamFixtures::class,
        ];
    }
}