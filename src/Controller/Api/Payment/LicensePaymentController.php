<?php

namespace App\Controller\Api\Payment;

use App\Dto\GenericResponse;
use Psr\Log\LoggerInterface;
use OpenApi\Attributes as OA;
use App\Entity\Activity\Activity;
use App\Entity\Security\UserMeta;
use App\Entity\Payment\LicensePlan;
use Nelmio\ApiDocBundle\Annotation\Model;
use App\Repository\Security\UserRepository;
use App\Repository\Shared\SettingRepository;
use Nelmio\ApiDocBundle\Annotation\Security;

use Symfony\Component\HttpFoundation\Request;
use FOS\RestBundle\Controller\Annotations as Rest;
use App\Controller\Api\Shared\AbstractBaseApiController;
use App\Service\Payment\Implementation\LicensePaymentService;

#[OA\Tag(name: 'Payments')]
class LicensePaymentController extends AbstractBaseApiController
{
    private LicensePaymentService $service;


  
    public function __construct(LicensePaymentService $service)
    {
      $this->service = $service;

    }



      /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/payments/licenses/subscribe", name: "api_payments_licenses_subscribe")]
  public function subscribe(Request $request)
  {
    $payUrl = $this->container->get('request_stack')->getCurrentRequest()->getSchemeAndHttpHost() . "/public/payments/";
    $data = $this->service->subscribeLicense($request, $payUrl);
    return $this->result($data);
  }

      /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/payments/licenses/plans/list", name: "api_payments_licenses_plans_list")]
  public function getLicensePlans(Request $request)
  {
    $data = $this->service->getLicensePlans($request);
    return $this->result($data);
  }

      /**
   * List the details of the specified subscription plan.
   *
   * This call return the details of the specified subscription plan.
   */
  #[OA\Parameter(
    name: 'id',
    in: 'path',
    description: 'License plan id',
    required: true,
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the specific subscription plan',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: LicensePlan::class))
    )
  )]
  #[Security(name: 'Bearer')]
  #[Rest\Get("/payments/licenses/plans/{id}", name: "api_payments_license_plan_id")]
  public function getLicensePlan(mixed $id)
  {
    $data = $this->service->getLicensePlan($id);
    return $this->result($data);
  }

       /**
   * Update the details of the specified subscription plan.
   *
   * This call takes into account the specified subscription plan.
   */
  #[Rest\Patch("/payments/licenses/plans/{id}", name: "api_payments_license_plans_id_patch")]
  #[OA\Parameter(
    name: 'id',
    in: 'path',
    description: 'License plan id',
    required: true,
  )]
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(ref: new Model(type: LicensePlan::class))
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the updated subscription plan',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: LicensePlan::class))
    )
  )]
  #[Security(name: 'Bearer')]
  public function updateLicensePlan(Request $request, mixed $id)
  {
    $data = $this->service->updateLicensePlan($request, $id);
    return $this->result($data);  
  }

    /**
   * Enable or disable the specified subscription plan.
   *
   * This call enables or disables the specified subscription plan depending on the current status.
   */
  #[OA\Parameter(
    name: 'id',
    in: 'path',
    description: 'License plan id',
    required: true,
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the enabled or disabled subscription plan',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: LicensePlan::class))
    )
  )]
  #[Security(name: 'Bearer')]
  #[Rest\Post("/payments/licenses/plans/toggle/{id}", name: "api_payments_license_plan_toggle")]
  public function toggleLicensePlan(mixed $id)
  {
    $data = $this->service->toggleLicensePlan($id);
    return $this->result($data);
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/payments/licenses/list", name: "api_payments_licenses_list")]
  public function getLicenses()
  {
    $data = $this->service->getLicenses();
    return $this->result($data);
  }

        /**
   * For creating a new subscription plan.
   *
   * This call allows to create a new subscription plan.
   */
  #[Rest\Post("/payments/licenses/plans/create", name: "api_payments_license_plans_create")]
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(ref: new Model(type: LicensePlan::class))
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the created license plan',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: LicensePlan::class))
    )
  )]
#[Security(name: 'Bearer')]
  public function createLicensePlan(Request $request)
  {
    $data = $this->service->createLicensePlan($request);
    return $this->result($data);
  }

      /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/payments/licenses/payments/list", name: "api_licenses_payments_list")]
  public function getLicensesPayments(Request $request)
  {
    $data = $this->service->getLicensesPayments( $request);
    return $this->result($data);
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/payments/licenses/check", name: "api_public_payments_licenses_check")]
  public function checkLicenses()
  {

    $data = $this->service->checkLicenses();
    return $this->result($data);
  }



    /**
     * Validate a license.
     *
     * This endpoint allows administrators to validate a license by marking it as checked.
     */
    #[Rest\Post("/payments/licenses/validate/{id}/{validate}", name: "api_payments_licenses_validate")]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'License id',
        required: true,
    )]
    #[OA\Parameter(
      name: 'validate',
      in: 'path',
      description: 'Decision to validate or not the license',
      required: true,
  )]
    #[OA\Response(
        response: 200,
        description: 'Returns the validated license'
    )]
    #[Security(name: 'Bearer')]
    public function validateLicense(mixed $id, mixed $validate)
    {
        $data = $this->service->validateLicense($id, $validate);
        return $this->result($data);
    }

    /**
     * Get all licenses with administrative details.
     *
     * This endpoint returns all licenses grouped by company and category, 
     * accessible only to administrators.
     */
    #[Rest\Get("/payments/licenses/admin/list", name: "api_payments_licenses_admin_list")]
    #[OA\Response(
        response: 200,
        description: 'Returns all licenses grouped by company and category'
    )]
    #[Security(name: 'Bearer')]
    public function getLicensesAdmin()
    {
        $data = $this->service->getLicensesAdmin();
        return $this->result($data);
    }

}