<?php

namespace App\Service\Tournament\Implementation;

use App\Dto\GenericResponse;
use App\Dto\GenericResponseMessage;
use App\Constant\GenericResponseStatuses;
use App\Constant\GenericResponseMessageTypes;
use App\Entity\Tournament\Encounter;
use App\Entity\Tournament\EncounterEvent;
use App\Repository\Tournament\EncounterRepository;
use App\Repository\Tournament\EncounterEventRepository;
use App\Repository\Tournament\TournamentRepository;
use App\Repository\Tournament\TournamentDayRepository;
use App\Repository\Team\TeamRepository;
use App\Repository\Player\PlayerRepository;
use App\Service\Tournament\Interface\IEncounterService;
use App\Service\Traits\BaseServiceTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Security;

class EncounterService implements IEncounterService
{
    use BaseServiceTrait;

    private EncounterRepository $encounterRepository;
    private EncounterEventRepository $eventRepository;
    private TournamentRepository $tournamentRepository;
    private TournamentDayRepository $tournamentDayRepository;
    private TeamRepository $teamRepository;
    private PlayerRepository $playerRepository;
    private EntityManagerInterface $entityManager;
    private Security $security;

    public function __construct(
        EncounterRepository $encounterRepository,
        EncounterEventRepository $eventRepository,
        TournamentRepository $tournamentRepository,
        TournamentDayRepository $tournamentDayRepository,
        TeamRepository $teamRepository,
        PlayerRepository $playerRepository,
        EntityManagerInterface $entityManager,
        Security $security
    ) {
        $this->encounterRepository = $encounterRepository;
        $this->eventRepository = $eventRepository;
        $this->tournamentRepository = $tournamentRepository;
        $this->tournamentDayRepository = $tournamentDayRepository;
        $this->teamRepository = $teamRepository;
        $this->playerRepository = $playerRepository;
        $this->entityManager = $entityManager;
        $this->security = $security;
    }

    public function createEncounter(Request $request): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                $message = new GenericResponseMessage('Access denied. Admin privileges required.', 'access_denied', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $data = json_decode($request->getContent(), true);

            // Validate teams are from same category
            $homeTeam = $this->teamRepository->findOneBy(['uuid' => $data['homeTeamId'], 'active' => true]);
            $awayTeam = $this->teamRepository->findOneBy(['uuid' => $data['awayTeamId'], 'active' => true]);

            if (!$homeTeam || !$awayTeam) {
                $message = new GenericResponseMessage('One or both teams not found', 'teams_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            if ($homeTeam->getCategory() !== $awayTeam->getCategory()) {
                $message = new GenericResponseMessage('Teams must be from the same category', 'teams_category_mismatch', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
            }

            $encounter = new Encounter();
            $encounter->setHomeTeam($homeTeam)
                     ->setAwayTeam($awayTeam)
                     ->setCategory($homeTeam->getCategory()->getName())
                     ->setEncounterDate(new \DateTime($data['encounterDate']))
                     ->setLocation($data['location'] ?? null)
                     ->setStatus($data['status'] ?? 'SCHEDULED')
                     ->setNotes($data['notes'] ?? null)
                     ->setActive(true);

            // Add to tournament day (required)
            if (isset($data['tournamentDayId'])) {
                $tournamentDay = $this->tournamentDayRepository->getByUuid($data['tournamentDayId']);
                if ($tournamentDay) {
                    $encounter->setTournamentDay($tournamentDay);
                } else {
                    $message = new GenericResponseMessage('Tournament day not found');
                    return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
                }
            } else {
                $message = new GenericResponseMessage('Tournament day ID is required');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
            }

            $this->encounterRepository->save($encounter, true);

            $message = new GenericResponseMessage('Encounter created successfully', 'encounter_created', GenericResponseMessageTypes::SUCCESS);
            return $this->result([
                'encounter' => $this->formatEncounterData($encounter)
            ], GenericResponseStatuses::SUCCESS, Response::HTTP_OK, [$message]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error creating encounter: ' . $e->getMessage(), 'encounter_creation_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getAllEncounters(Request $request): GenericResponse
    {
        try {
            $status = $request->query->get('status');
            $category = $request->query->get('category');
            $tournamentId = $request->query->get('tournamentId');

            $criteria = ['active' => true];
            if ($status) $criteria['status'] = $status;
            if ($category) $criteria['category'] = $category;

            $encounters = $this->encounterRepository->findBy($criteria, ['encounterDate' => 'ASC']);

            // Filter by tournament if specified
            if ($tournamentId) {
                $encounters = array_filter($encounters, function($encounter) use ($tournamentId) {
                    return $encounter->getTournament() && $encounter->getTournament()->getUuid() === $tournamentId;
                });
            }

            $formattedEncounters = array_map(
                fn($encounter) => $this->formatEncounterData($encounter),
                $encounters
            );

            return $this->result([
                'encounters' => $formattedEncounters,
                'total' => count($formattedEncounters)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching encounters: ' . $e->getMessage(), 'encounters_fetch_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getEncounter(string $id): GenericResponse
    {
        try {
            $encounter = $this->encounterRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$encounter) {
                $message = new GenericResponseMessage('Encounter not found', 'encounter_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            return $this->result([
                'encounter' => $this->formatEncounterData($encounter, true)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching encounter: ' . $e->getMessage(), 'encounter_fetch_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function updateEncounter(Request $request, string $id): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                $message = new GenericResponseMessage('Access denied. Admin privileges required.', 'access_denied', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $encounter = $this->encounterRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$encounter) {
                $message = new GenericResponseMessage('Encounter not found', 'encounter_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            $data = json_decode($request->getContent(), true);

            if (isset($data['encounterDate'])) $encounter->setEncounterDate(new \DateTime($data['encounterDate']));
            if (isset($data['location'])) $encounter->setLocation($data['location']);
            if (isset($data['status'])) $encounter->setStatus($data['status']);
            if (isset($data['notes'])) $encounter->setNotes($data['notes']);

            $this->encounterRepository->save($encounter, true);

            $message = new GenericResponseMessage('Encounter updated successfully', 'encounter_updated', GenericResponseMessageTypes::SUCCESS);
            return $this->result([
                'encounter' => $this->formatEncounterData($encounter)
            ], GenericResponseStatuses::SUCCESS, Response::HTTP_OK, [$message]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error updating encounter: ' . $e->getMessage(), 'encounter_update_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function deleteEncounter(string $id): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                $message = new GenericResponseMessage('Access denied. Admin privileges required.', 'access_denied', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $encounter = $this->encounterRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$encounter) {
                $message = new GenericResponseMessage('Encounter not found', 'encounter_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            $encounter->setActive(false);
            $this->encounterRepository->save($encounter, true);

            $message = new GenericResponseMessage('Encounter deleted successfully', 'encounter_deleted', GenericResponseMessageTypes::SUCCESS);
            return $this->result([], GenericResponseStatuses::SUCCESS, Response::HTTP_OK, [$message]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error deleting encounter: ' . $e->getMessage(), 'encounter_delete_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function addEventToEncounter(Request $request): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                $message = new GenericResponseMessage('Access denied. Admin privileges required.', 'access_denied', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $data = json_decode($request->getContent(), true);

            $encounter = $this->encounterRepository->findOneBy(['uuid' => $data['encounterId'], 'active' => true]);
            $player = $this->playerRepository->findOneBy(['uuid' => $data['playerId'], 'active' => true]);

            if (!$encounter) {
                $message = new GenericResponseMessage('Encounter not found', 'encounter_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            if (!$player) {
                $message = new GenericResponseMessage('Player not found', 'player_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            // Validate player is in one of the teams
            if ($player->getTeam() !== $encounter->getHomeTeam() && $player->getTeam() !== $encounter->getAwayTeam()) {
                $message = new GenericResponseMessage('Player is not part of either team in this encounter', 'player_team_mismatch', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
            }

            $event = new EncounterEvent();
            $event->setEncounter($encounter)
                  ->setPlayer($player)
                  ->setType($data['type']) // GOAL, YELLOW_CARD, RED_CARD
                  ->setMinute($data['minute'] ?? null)
                  ->setDescription($data['description'] ?? null);

            // Handle assist for goals
            if ($data['type'] === 'GOAL' && isset($data['assistPlayerId'])) {
                $assistPlayer = $this->playerRepository->findOneBy(['uuid' => $data['assistPlayerId'], 'active' => true]);
                if ($assistPlayer) {
                    $event->setAssistPlayer($assistPlayer);
                }
            }

            $this->eventRepository->save($event, true);

            // Update player statistics
            $this->updatePlayerStatistics($player, $data['type']);

            // Update encounter score if it's a goal
            if ($data['type'] === 'GOAL') {
                $this->updateEncounterScoreFromEvents($encounter);
            }

            $message = new GenericResponseMessage('Event added successfully', 'event_added', GenericResponseMessageTypes::SUCCESS);
            return $this->result([
                'event' => $this->formatEventData($event)
            ], GenericResponseStatuses::SUCCESS, Response::HTTP_OK, [$message]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error adding event: ' . $e->getMessage(), 'event_add_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function removeEventFromEncounter(string $eventId): GenericResponse
    {
        // Implementation will be added in next part
        return $this->result([]);
    }

    public function getEncountersByTournament(string $tournamentId): GenericResponse
    {
        // Implementation will be added in next part
        return $this->result([]);
    }

    public function getEncountersByTeam(string $teamId): GenericResponse
    {
        // Implementation will be added in next part
        return $this->result([]);
    }

    public function getUpcomingEncounters(): GenericResponse
    {
        // Implementation will be added in next part
        return $this->result([]);
    }

    public function getCompletedEncounters(): GenericResponse
    {
        // Implementation will be added in next part
        return $this->result([]);
    }

    public function updateEncounterScore(Request $request, string $id): GenericResponse
    {
        // Implementation will be added in next part
        return $this->result([]);
    }

    private function formatEncounterData(Encounter $encounter, bool $detailed = false): array
    {
        $data = [
            'id' => $encounter->getId(),
            'uuid' => $encounter->getUuid(),
            'homeTeam' => [
                'id' => $encounter->getHomeTeam()->getId(),
                'uuid' => $encounter->getHomeTeam()->getUuid(),
                'name' => $encounter->getHomeTeam()->getName(),
            ],
            'awayTeam' => [
                'id' => $encounter->getAwayTeam()->getId(),
                'uuid' => $encounter->getAwayTeam()->getUuid(),
                'name' => $encounter->getAwayTeam()->getName(),
            ],
            'homeScore' => $encounter->getHomeScore(),
            'awayScore' => $encounter->getAwayScore(),
            'category' => $encounter->getCategory(),
            'encounterDate' => $encounter->getEncounterDate()?->format('Y-m-d H:i:s'),
            'location' => $encounter->getLocation(),
            'status' => $encounter->getStatus(),
            'createdAt' => $encounter->getCreatedAt()?->format('Y-m-d H:i:s'),
        ];

        if ($detailed) {
            $data['notes'] = $encounter->getNotes();
            $data['tournament'] = $encounter->getTournament() ? [
                'id' => $encounter->getTournament()->getId(),
                'uuid' => $encounter->getTournament()->getUuid(),
                'name' => $encounter->getTournament()->getName(),
            ] : null;
            $data['tournamentDay'] = $encounter->getTournamentDay() ? [
                'id' => $encounter->getTournamentDay()->getId(),
                'uuid' => $encounter->getTournamentDay()->getUuid(),
                'dayNumber' => $encounter->getTournamentDay()->getDayNumber(),
                'name' => $encounter->getTournamentDay()->getName(),
                'date' => $encounter->getTournamentDay()->getFormattedDate(),
                'dayStatus' => $encounter->getTournamentDay()->getDayStatus(),
            ] : null;
            $data['events'] = $encounter->getEvents()->map(fn($event) => $this->formatEventData($event))->toArray();
        }

        return $data;
    }

    private function formatEventData(EncounterEvent $event): array
    {
        return [
            'id' => $event->getId(),
            'uuid' => $event->getUuid(),
            'type' => $event->getType(),
            'minute' => $event->getMinute(),
            'description' => $event->getDescription(),
            'player' => [
                'id' => $event->getPlayer()->getId(),
                'uuid' => $event->getPlayer()->getUuid(),
                'firstName' => $event->getPlayer()->getFirstName(),
                'lastName' => $event->getPlayer()->getLastName(),
            ],
            'assistPlayer' => $event->getAssistPlayer() ? [
                'id' => $event->getAssistPlayer()->getId(),
                'uuid' => $event->getAssistPlayer()->getUuid(),
                'firstName' => $event->getAssistPlayer()->getFirstName(),
                'lastName' => $event->getAssistPlayer()->getLastName(),
            ] : null,
            'createdAt' => $event->getCreatedAt()?->format('Y-m-d H:i:s'),
        ];
    }

    private function updatePlayerStatistics($player, string $eventType): void
    {
        switch ($eventType) {
            case 'GOAL':
                $player->incrementGoals();
                break;
            case 'YELLOW_CARD':
                $player->incrementYellowCards();
                break;
            case 'RED_CARD':
                $player->incrementRedCards();
                break;
        }

        $this->entityManager->persist($player);
        $this->entityManager->flush();
    }

    private function updateEncounterScoreFromEvents(Encounter $encounter): void
    {
        $homeGoals = 0;
        $awayGoals = 0;

        foreach ($encounter->getGoalEvents() as $goalEvent) {
            if ($goalEvent->getPlayer()->getTeam() === $encounter->getHomeTeam()) {
                $homeGoals++;
            } else {
                $awayGoals++;
            }
        }

        $encounter->setHomeScore($homeGoals);
        $encounter->setAwayScore($awayGoals);

        $this->entityManager->persist($encounter);
        $this->entityManager->flush();
    }

    private function isAdmin(): bool
    {
        $user = $this->security->getUser();
        if (!$user) return false;

        // Check if user has admin meta
        foreach ($user->getMetas() as $meta) {
            if ($meta->getCode() === 'is_admin' && $meta->getValue() === '1') {
                return true;
            }
            if ($meta->getCode() === 'is_super_admin' && $meta->getValue() === '1') {
                return true;
            }
        }

        return false;
    }
}
