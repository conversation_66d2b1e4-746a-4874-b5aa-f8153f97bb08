<?php

namespace App\Entity\Tournament;

use App\Entity\Company\Company;
use App\Entity\Team\TeamCategory;
use App\Entity\Tournament\Encounter;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Tournament\TournamentRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "tournaments")]
#[ORM\Entity(repositoryClass: TournamentRepository::class)]
class Tournament
{
    use IDableTrait;
    use UuidableTrait;
    use TimestampableTrait;
    use DeActivatableTrait;
    use DescribableTrait;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $startDate = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $endDate = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?TeamCategory $category = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $location = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $rules = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $maxTeams = null;

    #[ORM\Column(length: 50)]
    private string $status = 'DRAFT';

    #[ORM\ManyToMany(targetEntity: Company::class)]
    #[ORM\JoinTable(name: 'tournament_companies')]
    private Collection $companies;

    // Encounters are now managed through TournamentDay relationship
    // Use getTournamentDays()->getEncounters() to access encounters

    #[ORM\OneToMany(targetEntity: TournamentDay::class, mappedBy: 'tournament', cascade: ['persist', 'remove'])]
    private Collection $tournamentDays;

    public function __construct()
    {
        $this->companies = new ArrayCollection();
        $this->tournamentDays = new ArrayCollection();
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;
        return $this;
    }

    public function getStartDate(): ?\DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTimeInterface $startDate): static
    {
        $this->startDate = $startDate;
        return $this;
    }

    public function getEndDate(): ?\DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTimeInterface $endDate): static
    {
        $this->endDate = $endDate;
        return $this;
    }

    public function getCategory(): ?TeamCategory
    {
        return $this->category;
    }

    public function setCategory(?TeamCategory $category): static
    {
        $this->category = $category;
        return $this;
    }

    public function getLocation(): ?string
    {
        return $this->location;
    }

    public function setLocation(?string $location): static
    {
        $this->location = $location;
        return $this;
    }

    public function getRules(): ?string
    {
        return $this->rules;
    }

    public function setRules(?string $rules): static
    {
        $this->rules = $rules;
        return $this;
    }

    public function getMaxTeams(): ?int
    {
        return $this->maxTeams;
    }

    public function setMaxTeams(?int $maxTeams): static
    {
        $this->maxTeams = $maxTeams;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return Collection<int, Company>
     */
    public function getCompanies(): Collection
    {
        return $this->companies;
    }

    public function addCompany(Company $company): static
    {
        if (!$this->companies->contains($company)) {
            $this->companies->add($company);
        }
        return $this;
    }

    public function removeCompany(Company $company): static
    {
        $this->companies->removeElement($company);
        return $this;
    }

    /**
     * Get all encounters across all tournament days
     * @return array<Encounter>
     */
    public function getEncounters(): array
    {
        $encounters = [];
        foreach ($this->tournamentDays as $day) {
            foreach ($day->getEncounters() as $encounter) {
                $encounters[] = $encounter;
            }
        }
        return $encounters;
    }

    /**
     * Get encounters count across all tournament days
     */
    public function getEncountersCount(): int
    {
        $count = 0;
        foreach ($this->tournamentDays as $day) {
            $count += $day->getEncountersCount();
        }
        return $count;
    }

    /**
     * @return Collection<int, TournamentDay>
     */
    public function getTournamentDays(): Collection
    {
        return $this->tournamentDays;
    }

    public function addTournamentDay(TournamentDay $tournamentDay): static
    {
        if (!$this->tournamentDays->contains($tournamentDay)) {
            $this->tournamentDays->add($tournamentDay);
            $tournamentDay->setTournament($this);
        }
        return $this;
    }

    public function removeTournamentDay(TournamentDay $tournamentDay): static
    {
        if ($this->tournamentDays->removeElement($tournamentDay)) {
            if ($tournamentDay->getTournament() === $this) {
                $tournamentDay->setTournament(null);
            }
        }
        return $this;
    }

    /**
     * Get tournament days count
     */
    public function getTournamentDaysCount(): int
    {
        return $this->tournamentDays->count();
    }

    /**
     * Get tournament days ordered by day number
     */
    public function getTournamentDaysOrdered(): array
    {
        $days = $this->tournamentDays->toArray();
        usort($days, function(TournamentDay $a, TournamentDay $b) {
            return $a->getDayNumber() <=> $b->getDayNumber();
        });

        return $days;
    }
}
