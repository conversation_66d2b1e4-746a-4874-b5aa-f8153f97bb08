<?php

namespace App\Controller\Api\Tournament;

use App\Service\Tournament\Implementation\TournamentDayService;
use App\Controller\Api\Shared\AbstractBaseApiController;
use Symfony\Component\HttpFoundation\Request;
use FOS\RestBundle\Controller\Annotations as Rest;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Security;

#[Rest\Route('/tournament-days')]
#[OA\Tag(name: 'Tournament Days')]
class TournamentDayController extends AbstractBaseApiController
{
    private TournamentDayService $service;

    public function __construct(TournamentDayService $service)
    {
        $this->service = $service;
    }

    /**
     * Create a new tournament day
     */
    #[Rest\Post('/create')]
    #[OA\Post(
        path: '/api/v2/tournament-days/create',
        summary: 'Create a new tournament day',
        description: 'Creates a new tournament day for a specific tournament (admin only)'
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            type: "object",
            required: ["tournamentId", "date"],
            properties: [
                new OA\Property(property: 'tournamentId', type: 'string', example: 'tournament-uuid', description: 'UUID of the tournament'),
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2024-06-03', description: 'Date of the tournament day'),
                new OA\Property(property: 'dayNumber', type: 'integer', example: 2, description: 'Day number (auto-generated if not provided)'),
                new OA\Property(property: 'name', type: 'string', example: 'Group Stage Day', description: 'Name of the tournament day'),
                new OA\Property(property: 'description', type: 'string', example: 'Group stage matches for all categories', description: 'Description of the day')
            ]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Tournament day created successfully',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 201),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'tournamentDay',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'day-uuid'),
                                new OA\Property(property: 'dayNumber', type: 'integer', example: 2),
                                new OA\Property(property: 'name', type: 'string', example: 'Group Stage Day'),
                                new OA\Property(property: 'description', type: 'string', example: 'Group stage matches'),
                                new OA\Property(property: 'date', type: 'string', example: '2024-06-03'),
                                new OA\Property(property: 'dayStatus', type: 'string', example: 'SCHEDULED'),
                                new OA\Property(property: 'encountersCount', type: 'integer', example: 0),
                                new OA\Property(property: 'active', type: 'boolean', example: true),
                                new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(response: 400, description: 'Validation error')]
    #[OA\Response(response: 404, description: 'Tournament not found')]
    #[Security(name: 'Bearer')]
    public function createTournamentDay(Request $request)
    {
        return $this->result($this->service->createTournamentDay($request));
    }

    /**
     * Get all tournament days for a tournament
     */
    #[Rest\Get('/tournament/{tournamentId}')]
    #[OA\Get(
        path: '/api/v2/tournament-days/tournament/{tournamentId}',
        summary: 'Get tournament days for a tournament',
        description: 'Retrieves all tournament days for a specific tournament with statistics'
    )]
    #[OA\Parameter(
        name: 'tournamentId',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of tournament days with statistics',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 200),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'uuid', type: 'string', example: 'day-uuid'),
                            new OA\Property(property: 'dayNumber', type: 'integer', example: 1),
                            new OA\Property(property: 'name', type: 'string', example: 'Opening Day'),
                            new OA\Property(property: 'date', type: 'string', example: '2024-06-01'),
                            new OA\Property(property: 'dayStatus', type: 'string', example: 'COMPLETED'),
                            new OA\Property(property: 'encountersCount', type: 'integer', example: 4),
                            new OA\Property(
                                property: 'statistics',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'totalEncounters', type: 'integer', example: 4),
                                    new OA\Property(property: 'completedEncounters', type: 'integer', example: 4),
                                    new OA\Property(property: 'scheduledEncounters', type: 'integer', example: 0),
                                    new OA\Property(property: 'liveEncounters', type: 'integer', example: 0)
                                ]
                            )
                        ]
                    )
                )
            ]
        )
    )]
    #[OA\Response(response: 404, description: 'Tournament not found')]
    public function getTournamentDays(string $tournamentId)
    {
        return $this->result($this->service->getTournamentDays($tournamentId));
    }

    /**
     * Get tournament day by ID
     */
    #[Rest\Get('/show/{id}')]
    #[OA\Get(
        path: '/api/v2/tournament-days/show/{id}',
        summary: 'Get tournament day by ID',
        description: 'Retrieves a tournament day by ID with detailed information'
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament day UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament day details',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 200),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'uuid', type: 'string', example: 'day-uuid'),
                        new OA\Property(property: 'dayNumber', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Opening Day'),
                        new OA\Property(property: 'description', type: 'string', example: 'Tournament opening day'),
                        new OA\Property(property: 'date', type: 'string', example: '2024-06-01'),
                        new OA\Property(property: 'dayStatus', type: 'string', example: 'COMPLETED'),
                        new OA\Property(property: 'encountersCount', type: 'integer', example: 4),
                        new OA\Property(
                            property: 'tournament',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'tournament-uuid'),
                                new OA\Property(property: 'name', type: 'string', example: 'Summer Championship 2024')
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(response: 404, description: 'Tournament day not found')]
    public function getTournamentDay(string $id)
    {
        return $this->result($this->service->getTournamentDay($id));
    }

    /**
     * Update tournament day
     */
    #[Rest\Put('/update/{id}')]
    #[OA\Put(
        path: '/api/v2/tournament-days/update/{id}',
        summary: 'Update tournament day',
        description: 'Updates a tournament day (admin only)'
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament day UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'name', type: 'string', example: 'Updated Day Name'),
                new OA\Property(property: 'description', type: 'string', example: 'Updated description'),
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2024-06-04'),
                new OA\Property(property: 'dayNumber', type: 'integer', example: 3)
            ]
        )
    )]
    #[OA\Response(response: 200, description: 'Tournament day updated successfully')]
    #[OA\Response(response: 400, description: 'Validation error')]
    #[OA\Response(response: 404, description: 'Tournament day not found')]
    #[Security(name: 'Bearer')]
    public function updateTournamentDay(Request $request, string $id)
    {
        return $this->result($this->service->updateTournamentDay($request, $id));
    }

    /**
     * Delete tournament day
     */
    #[Rest\Delete('/delete/{id}')]
    #[OA\Delete(
        path: '/api/v2/tournament-days/delete/{id}',
        summary: 'Delete tournament day',
        description: 'Deletes a tournament day (admin only)'
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament day UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(response: 200, description: 'Tournament day deleted successfully')]
    #[OA\Response(response: 400, description: 'Cannot delete day with existing encounters')]
    #[OA\Response(response: 404, description: 'Tournament day not found')]
    #[Security(name: 'Bearer')]
    public function deleteTournamentDay(string $id)
    {
        return $this->result($this->service->deleteTournamentDay($id));
    }

    /**
     * Get encounters for a tournament day
     */
    #[Rest\Get('/{id}/encounters')]
    #[OA\Get(
        path: '/api/v2/tournament-days/{id}/encounters',
        summary: 'Get encounters for a tournament day',
        description: 'Retrieves all encounters scheduled for a specific tournament day'
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament day UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(response: 200, description: 'List of encounters for the day')]
    #[OA\Response(response: 404, description: 'Tournament day not found')]
    public function getDayEncounters(string $id)
    {
        return $this->result($this->service->getDayEncounters($id));
    }

    /**
     * Get tournament day statistics
     */
    #[Rest\Get('/{id}/statistics')]
    #[OA\Get(
        path: '/api/v2/tournament-days/{id}/statistics',
        summary: 'Get tournament day statistics',
        description: 'Retrieves statistics for a specific tournament day'
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament day UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(response: 200, description: 'Tournament day statistics')]
    #[OA\Response(response: 404, description: 'Tournament day not found')]
    public function getDayStatistics(string $id)
    {
        return $this->result($this->service->getDayStatistics($id));
    }

    /**
     * Get upcoming tournament days
     */
    #[Rest\Get('/upcoming')]
    #[OA\Get(
        path: '/api/v2/tournament-days/upcoming',
        summary: 'Get upcoming tournament days',
        description: 'Retrieves upcoming tournament days across all tournaments'
    )]
    #[OA\Parameter(
        name: 'limit',
        in: 'query',
        description: 'Maximum number of days to return',
        required: false,
        schema: new OA\Schema(type: 'integer', default: 10)
    )]
    #[OA\Response(response: 200, description: 'List of upcoming tournament days')]
    public function getUpcomingDays(Request $request)
    {
        return $this->result($this->service->getUpcomingDays($request));
    }

    /**
     * Get today's tournament days
     */
    #[Rest\Get('/today')]
    #[OA\Get(
        path: '/api/v2/tournament-days/today',
        summary: 'Get today\'s tournament days',
        description: 'Retrieves all tournament days scheduled for today'
    )]
    #[OA\Response(response: 200, description: 'List of today\'s tournament days')]
    public function getTodaysDays()
    {
        return $this->result($this->service->getTodaysDays());
    }
}
