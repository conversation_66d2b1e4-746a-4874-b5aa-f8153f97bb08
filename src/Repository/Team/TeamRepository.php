<?php

namespace App\Repository\Team;

use App\Entity\Team\Team;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Team>
 *
 * @method Team|null find($id, $lockMode = null, $lockVersion = null)
 * @method Team|null findOneBy(array $criteria, array $orderBy = null)
 * @method Team[]    findAll()
 * @method Team[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TeamRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Team::class);
    }

    public function save(Team $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Team $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find team by UUID
     */
    public function getByUuid(string $uuid): ?Team
    {
        return $this->findOneBy(['uuid' => $uuid, 'active' => true]);
    }

    /**
     * Find teams by company
     */
    public function findByCompany(int $companyId): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.company = :companyId')
            ->setParameter('companyId', $companyId)
            ->andWhere('t.active = :active')
            ->setParameter('active', true)
            ->orderBy('t.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find teams by category
     */
    public function findByCategory(string $category): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.category = :category')
            ->setParameter('category', $category)
            ->andWhere('t.active = :active')
            ->setParameter('active', true)
            ->orderBy('t.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find teams with player count
     */
    public function findTeamsWithPlayerCount(): array
    {
        return $this->createQueryBuilder('t')
            ->select('t, COUNT(p.id) as playerCount')
            ->leftJoin('t.players', 'p')
            ->andWhere('t.active = :active')
            ->setParameter('active', true)
            ->groupBy('t.id')
            ->orderBy('t.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search teams by name
     */
    public function searchByName(string $name): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.name LIKE :name')
            ->setParameter('name', '%' . $name . '%')
            ->andWhere('t.active = :active')
            ->setParameter('active', true)
            ->orderBy('t.name', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
