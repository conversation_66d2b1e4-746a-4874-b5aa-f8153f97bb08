<?php

namespace App\Controller\Api\Security;

use OpenApi\Attributes as OA;
use App\Dto\Security\In\AuthenticateIn;
use Nelmio\ApiDocBundle\Annotation\Model;
use App\Dto\Security\Out\User\UserFullOut;
use App\Dto\Security\In\AuthenticateVerifyIn;
use Symfony\Component\HttpFoundation\Request;
use App\Service\Security\Interface\IUserService;
use App\Controller\Api\Shared\AbstractBaseApiController;
use FOS\RestBundle\Controller\Annotations as Rest;
use App\Service\Security\Implementation\UserService;

#[OA\Tag(name: 'Authentication')]
class UsersController extends AbstractBaseApiController
{
  private UserService $service;

  public function __construct(UserService $userService)
  {
    $this->service = $userService;
  }

  /**
   * Authenticate user credentials
   *
   * Validates user credentials and returns authentication token for API access.
   * Supports both email/password and phone/password authentication methods.
   */
  #[OA\Post(
    path: "/api/v2/users/authenticate",
    summary: "User Authentication",
    description: "Authenticate user with email/phone and password to obtain access token",
    tags: ["Authentication"]
  )]
  #[OA\RequestBody(
    required: true,
    description: "User credentials for authentication",
    content: new OA\JsonContent(
      required: ["login", "password"],
      properties: [
        new OA\Property(
          property: "login",
          type: "string",
          description: "User email or phone number",
          example: "<EMAIL>"
        ),
        new OA\Property(
          property: "password",
          type: "string",
          description: "User password",
          example: "SecurePassword123!"
        ),
        new OA\Property(
          property: "remember",
          type: "boolean",
          description: "Remember user session",
          example: true
        )
      ]
    )
  )]
  #[OA\Response(
    response: 200,
    description: "Authentication successful",
    content: new OA\JsonContent(
      properties: [
        new OA\Property(property: "code", type: "integer", example: 200),
        new OA\Property(property: "status", type: "string", example: "success"),
        new OA\Property(
          property: "data",
          type: "object",
          properties: [
            new OA\Property(property: "token", type: "string", example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."),
            new OA\Property(property: "refreshToken", type: "string", example: "def50200..."),
            new OA\Property(property: "expiresAt", type: "string", format: "date-time", example: "2024-12-31T23:59:59Z"),
            new OA\Property(
              property: "user",
              type: "object",
              properties: [
                new OA\Property(property: "id", type: "integer", example: 1),
                new OA\Property(property: "uuid", type: "string", example: "550e8400-e29b-41d4-a716-************"),
                new OA\Property(property: "email", type: "string", example: "<EMAIL>"),
                new OA\Property(property: "firstName", type: "string", example: "John"),
                new OA\Property(property: "lastName", type: "string", example: "Doe"),
                new OA\Property(property: "phone", type: "string", example: "+************"),
                new OA\Property(property: "roles", type: "array", items: new OA\Items(type: "string"), example: ["ROLE_USER"])
              ]
            )
          ]
        )
      ]
    )
  )]
  #[OA\Response(
    response: 401,
    description: "Authentication failed",
    content: new OA\JsonContent(
      properties: [
        new OA\Property(property: "code", type: "integer", example: 401),
        new OA\Property(property: "status", type: "string", example: "error"),
        new OA\Property(property: "message", type: "string", example: "Invalid credentials")
      ]
    )
  )]
  #[OA\Response(
    response: 422,
    description: "Validation error",
    content: new OA\JsonContent(
      properties: [
        new OA\Property(property: "code", type: "integer", example: 422),
        new OA\Property(property: "status", type: "string", example: "error"),
        new OA\Property(property: "message", type: "string", example: "Validation failed"),
        new OA\Property(
          property: "errors",
          type: "object",
          properties: [
            new OA\Property(property: "login", type: "array", items: new OA\Items(type: "string"), example: ["This field is required"]),
            new OA\Property(property: "password", type: "array", items: new OA\Items(type: "string"), example: ["This field is required"])
          ]
        )
      ]
    )
  )]
  #[Rest\Post("/users/authenticate", name: "api_users_authenticate")]
  public function authenticate(Request $request)
  {
    return $this->result($this->service->authenticate($request));
  }

  /**
   * Verify authentication token
   *
   * Validates the provided JWT token and returns user information if valid.
   * Used to check token validity and refresh user session data.
   */
  #[OA\Post(
    path: "/api/v2/users/authenticate/verify",
    summary: "Verify Authentication Token",
    description: "Validate JWT token and retrieve current user information",
    tags: ["Authentication"]
  )]
  #[OA\RequestBody(
    required: true,
    description: "Authentication token to verify",
    content: new OA\JsonContent(
      required: ["token"],
      properties: [
        new OA\Property(
          property: "token",
          type: "string",
          description: "JWT authentication token",
          example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
        )
      ]
    )
  )]
  #[OA\Response(
    response: 200,
    description: "Token is valid",
    content: new OA\JsonContent(
      properties: [
        new OA\Property(property: "code", type: "integer", example: 200),
        new OA\Property(property: "status", type: "string", example: "success"),
        new OA\Property(
          property: "data",
          type: "object",
          properties: [
            new OA\Property(property: "valid", type: "boolean", example: true),
            new OA\Property(property: "expiresAt", type: "string", format: "date-time", example: "2024-12-31T23:59:59Z"),
            new OA\Property(
              property: "user",
              type: "object",
              properties: [
                new OA\Property(property: "id", type: "integer", example: 1),
                new OA\Property(property: "uuid", type: "string", example: "550e8400-e29b-41d4-a716-************"),
                new OA\Property(property: "email", type: "string", example: "<EMAIL>"),
                new OA\Property(property: "firstName", type: "string", example: "John"),
                new OA\Property(property: "lastName", type: "string", example: "Doe"),
                new OA\Property(property: "roles", type: "array", items: new OA\Items(type: "string"), example: ["ROLE_USER"])
              ]
            )
          ]
        )
      ]
    )
  )]
  #[OA\Response(
    response: 401,
    description: "Token is invalid or expired",
    content: new OA\JsonContent(
      properties: [
        new OA\Property(property: "code", type: "integer", example: 401),
        new OA\Property(property: "status", type: "string", example: "error"),
        new OA\Property(property: "message", type: "string", example: "Token is invalid or expired")
      ]
    )
  )]
  #[Rest\Post("/users/authenticate/verify", name: "api_users_authenticate_verify")]
  public function verify(Request $request)
  {
  }

   /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/registerprofile", name: "api_users_register_profile")]
  public function registerProfile(Request $request)
  {
    return $this->result($this->service->registerProfile($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/updateprofile", name: "api_users_update_profile")]
  public function updateProfile(Request $request)
  {
    return $this->result($this->service->updateProfile($request));
  }

     /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/setlanguage", name: "api_users_set_language")]
  public function setUserLanguage(Request $request)
  {
    return $this->result($this->service->setLanguage($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/removalRequest/{option}", name: "api_users_removal_request")]
  public function removalRequest(string $option)
  {
    return $this->result($this->service->removalRequest($option));
  }

     /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/admin/update", name: "api_admin_update_users")]
  public function updateUserAdmin(Request $request)
  {
    return $this->result($this->service->updateProfileAdmin($request));
  }
  
          /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Delete("/users/admin/delete/{uuid}", name: "api_delete_users_admin")]
  public function deleteUsersAdmin(string $uuid)
  {
    return $this->result($this->service->deleteUsersAdmin($uuid));
  }
  
    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/authenticationcodes/", name: "api_users_get_authentication_codes")]
  public function getAuthCodes()
  {
    return $this->result($this->service->getAllAuthCodes());
  }

  


  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/authenticate/info", name: "api_users_authenticate_info")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "full"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: UserFullOut::class))
    )
  )]
  public function info()
  {
    return $this->result($this->service->info());
  }



  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/admin/super/info", name: "api_users_super_admin_info")]
  public function isSuperAdmin()
  {

    return $this->result($this->service->isSuperAdmin());
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Patch("/users/{uuid}", name: "api_users_update")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "standard"
    ),
  )]
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(
      type: "array",
      items: new OA\Items(
        properties: [
          new OA\Property(property: "type", description: "Patch operation type", type: "string"),
          new OA\Property(property: "payload", description: "Patch operation data value", type: "object")
        ]
      ),
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user',
    content: new OA\JsonContent(ref: new Model(type: UserFullOut::class))
  )]
  public function update(string $uuid, Request $request)
  {
    return $this->result($this->service->update($uuid, $request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/{uuid}", name: "api_users_findOne")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "full"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: UserFullOut::class))
    )
  )]
  public function findOne(string $uuid, Request $request)
  {
    return $this->result($this->service->findOne($uuid, $request));
  }

      /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/info/{id}", name: "api_users_findonebyid")]
  public function findOneUserById(string $id)
  {
    return $this->result($this->service->findOneById($id));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/admin/list", name: "api_users_findall_admin")]
  public function getUsersAdmin(Request $request)
  {
    return $this->result($this->service->getUsers($request));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/reset_bonus", name: "api_users_reset_bonus")]
  public function resetBonus()
  {
    return $this->result($this->service->resetBonus());
  }

}
