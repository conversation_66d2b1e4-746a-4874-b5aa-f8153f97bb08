<?php

namespace App\Controller\Api\Activity;

use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Model;
use Symfony\Component\HttpFoundation\Request;
use App\Controller\Api\Shared\AbstractBaseApiController;
use App\Dto\Activity\Out\Activity\ActivityFullOut;
use FOS\RestBundle\Controller\Annotations as Rest;
use App\Service\Activity\Implementation\ActivityService;


#[OA\Tag(name: "Activities")]
class ActivitiesController extends AbstractBaseApiController
{
  private ActivityService $service;

  public function __construct(ActivityService $service)
  {
    $this->service = $service;
  }

    /**
   * Creates an activity in the database.
   *
   * This endpoint creates an activity and return the created activity.
   */
  #[Rest\Post("/activities/create", name: "api_activities_create_activity")]
  #[OA\RequestBody(  
      description: 'Create an activity',    
      content: new OA\JsonContent(
          properties: [
              new OA\Property(property: '_instantInformation', type:'object', 
                  properties: [
                      new OA\Property(property: '_businessId', type:'string'),
                      new OA\Property(property: '_title', type:'string'),
                      new OA\Property(property: '_picture', type:'string'),
                      new OA\Property(property: '_shortDescription', type:'string'),
                      new OA\Property(property: '_longDescription', type:'string'),
                      new OA\Property(property: '_category', type:'string'),
                      new OA\Property(property: '_price', type:'string')
                  ]
              ),
              new OA\Property(property: '_address', type:'object', 
                  properties: [
                      new OA\Property(property: '_address', type:'string'),
                      new OA\Property(property: '_zipCode', type:'string'),
                      new OA\Property(property: '_city', type:'string'),
                      new OA\Property(property: '_country', type:'string'),
                      new OA\Property(property: '_coords', type:'object', 
                          properties: [
                              new OA\Property(property: 'latitude', type:'number'),
                              new OA\Property(property: 'longitude', type:'number')
                          ]
                      )
                  ]
              ),

              new OA\Property(property: '_pro_only', type:'string')
          ]
      )
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the activity created',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: ActivityFullOut::class))
    )
    
  )]
  public function createActivity(Request $request)
  {
    return $this->result($this->service->createActivity($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/activities/pro/list", name: "api_activities_pro_list")]
  public function businessActivities(Request $request)
  {
    return $this->result($this->service->businessActivities($request));
  }


  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/activities/get", name: "api_activities_get")]
  public function getActivities(Request $request)
  {
    return $this->result($this->service->getActivities($request));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/activities/admin/list", name: "api_activities_admin_list")]
  public function getActivitiesAdmin()
  {
    return $this->result($this->service->getActivitiesAdmin());
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/activities/pro/{uuid}", name: "api_activities_pro_getone")]
  public function businessActivity(string $uuid)
  {
    $data = $this->service->businessActivity($uuid);

    return $this->result($data);
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/activities/edit/{uuid}", name: "api_activities_edit_activity")]
  public function editActivity(string $uuid, Request $request)
  {
    return $this->result($this->service->editActivity($uuid, $request));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/activities/admin/edit", name: "api_activities_edit_activity_admin")]
  public function editActivityAdmin(Request $request)
  {
    return $this->result($this->service->editActivityAdmin($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/activities/cities", name: "api_activities_cities")]
  public function getActivityCities()
  {
    return $this->result($this->service->getActivityCities());
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Delete("/activities/myactivities/{uuid}", name: "api_activities_delete_business_activities")]
  public function deleteActivity(string $uuid)
  {
    return $this->result($this->service->deleteActivity($uuid));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Delete("/activities/admin/delete/{uuid}", name: "api_delete_activities_admin")]
  public function deleteActivityAdmin(string $uuid)
  {
    return $this->result($this->service->deleteActivity($uuid));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/activities/partners", name: "api_commons_partners")]
  public function partners(Request $request)
  {
    return $this->result($this->service->listPartners($request));
  }
}
