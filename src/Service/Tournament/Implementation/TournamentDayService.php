<?php

namespace App\Service\Tournament\Implementation;

use App\Entity\Tournament\TournamentDay;
use App\Repository\Tournament\TournamentRepository;
use App\Repository\Tournament\TournamentDayRepository;
use App\Repository\Tournament\EncounterRepository;
use App\Service\Tournament\Interface\ITournamentDayService;
use App\Service\Traits\BaseServiceTrait;
use App\Constant\GenericResponseStatuses;
use App\Dto\GenericResponse;
use App\Dto\GenericResponseMessage;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Security;

class TournamentDayService implements ITournamentDayService
{
    use BaseServiceTrait;

    private TournamentDayRepository $tournamentDayRepository;
    private TournamentRepository $tournamentRepository;
    private EncounterRepository $encounterRepository;
    private Security $security;

    public function __construct(
        TournamentDayRepository $tournamentDayRepository,
        TournamentRepository $tournamentRepository,
        EncounterRepository $encounterRepository,
        Security $security
    ) {
        $this->tournamentDayRepository = $tournamentDayRepository;
        $this->tournamentRepository = $tournamentRepository;
        $this->encounterRepository = $encounterRepository;
        $this->security = $security;
    }

    public function createTournamentDay(Request $request): GenericResponse
    {
        $data = json_decode($request->getContent(), true);

        // Validate required fields
        if (!isset($data['tournamentId']) || !isset($data['date'])) {
            $message = new GenericResponseMessage('Tournament ID and date are required');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
        }

        // Find tournament
        $tournament = $this->tournamentRepository->getByUuid($data['tournamentId']);
        if (!$tournament) {
            $message = new GenericResponseMessage('Tournament not found');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
        }

        // Parse date
        try {
            $date = new \DateTime($data['date']);
        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Invalid date format');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
        }

        // Check if date is within tournament range
        if ($date < $tournament->getStartDate() || $date > $tournament->getEndDate()) {
            $message = new GenericResponseMessage('Date must be within tournament date range');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
        }

        // Check if date already exists for this tournament
        if ($this->tournamentDayRepository->dateExists($tournament, $date)) {
            $message = new GenericResponseMessage('A tournament day already exists for this date');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
        }

        // Get day number
        $dayNumber = isset($data['dayNumber'])
            ? (int)$data['dayNumber']
            : $this->tournamentDayRepository->getNextDayNumber($tournament);

        // Check if day number already exists
        if ($this->tournamentDayRepository->dayNumberExists($tournament, $dayNumber)) {
            $message = new GenericResponseMessage('Day number already exists for this tournament');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
        }

        // Create tournament day
        $tournamentDay = new TournamentDay();
        $tournamentDay->setTournament($tournament);
        $tournamentDay->setDate($date);
        $tournamentDay->setDayNumber($dayNumber);
        $tournamentDay->setName($data['name'] ?? "Day {$dayNumber}");
        $tournamentDay->setDescription($data['description'] ?? null);

        $this->tournamentDayRepository->save($tournamentDay, true);

        return $this->result([
            'tournamentDay' => $this->formatTournamentDayData($tournamentDay)
        ], GenericResponseStatuses::SUCCESS, Response::HTTP_CREATED);
    }

    public function getTournamentDays(string $tournamentId): GenericResponse
    {
        $tournament = $this->tournamentRepository->getByUuid($tournamentId);
        if (!$tournament) {
            $message = new GenericResponseMessage('Tournament not found');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
        }

        $tournamentDays = $this->tournamentDayRepository->findWithStatistics($tournament);

        $formattedDays = array_map(function($result) {
            $tournamentDay = $result[0]; // The TournamentDay entity
            $data = $this->formatTournamentDayData($tournamentDay);

            // Add statistics
            $data['statistics'] = [
                'totalEncounters' => (int)$result['totalEncounters'],
                'completedEncounters' => (int)$result['completedEncounters'],
                'scheduledEncounters' => (int)$result['scheduledEncounters'],
                'liveEncounters' => (int)$result['liveEncounters']
            ];

            return $data;
        }, $tournamentDays);

        return $this->result($formattedDays);
    }

    public function getTournamentDay(string $id): GenericResponse
    {
        $tournamentDay = $this->tournamentDayRepository->getByUuid($id);
        if (!$tournamentDay) {
            $message = new GenericResponseMessage('Tournament day not found');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
        }

        $data = $this->formatTournamentDayData($tournamentDay, true);

        return $this->result($data);
    }

    public function updateTournamentDay(Request $request, string $id): GenericResponse
    {
        $tournamentDay = $this->tournamentDayRepository->getByUuid($id);
        if (!$tournamentDay) {
            $message = new GenericResponseMessage('Tournament day not found');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
        }

        $data = json_decode($request->getContent(), true);

        // Update fields if provided
        if (isset($data['name'])) {
            $tournamentDay->setName($data['name']);
        }

        if (isset($data['description'])) {
            $tournamentDay->setDescription($data['description']);
        }

        if (isset($data['date'])) {
            try {
                $date = new \DateTime($data['date']);
                
                // Check if new date conflicts with existing days
                if (!$this->tournamentDayRepository->dateExists(
                    $tournamentDay->getTournament(),
                    $date,
                    $tournamentDay->getId()
                )) {
                    $tournamentDay->setDate($date);
                } else {
                    $message = new GenericResponseMessage('A tournament day already exists for this date');
                    return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
                }
            } catch (\Exception $e) {
                $message = new GenericResponseMessage('Invalid date format');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
            }
        }

        if (isset($data['dayNumber'])) {
            $dayNumber = (int)$data['dayNumber'];
            
            // Check if new day number conflicts
            if (!$this->tournamentDayRepository->dayNumberExists(
                $tournamentDay->getTournament(),
                $dayNumber,
                $tournamentDay->getId()
            )) {
                $tournamentDay->setDayNumber($dayNumber);
            } else {
                $message = new GenericResponseMessage('Day number already exists for this tournament');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
            }
        }

        $this->tournamentDayRepository->save($tournamentDay, true);

        return $this->result([
            'tournamentDay' => $this->formatTournamentDayData($tournamentDay)
        ]);
    }

    public function deleteTournamentDay(string $id): GenericResponse
    {
        $tournamentDay = $this->tournamentDayRepository->getByUuid($id);
        if (!$tournamentDay) {
            $message = new GenericResponseMessage('Tournament day not found');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
        }

        // Check if day has encounters
        if ($tournamentDay->getEncountersCount() > 0) {
            $message = new GenericResponseMessage('Cannot delete tournament day with existing encounters');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
        }

        // Soft delete
        $tournamentDay->setActive(false);
        $this->tournamentDayRepository->save($tournamentDay, true);

        return $this->result(null);
    }

    public function getDayEncounters(string $id): GenericResponse
    {
        $tournamentDay = $this->tournamentDayRepository->getByUuid($id);
        if (!$tournamentDay) {
            $message = new GenericResponseMessage('Tournament day not found');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
        }

        $encounters = $tournamentDay->getEncounters();

        $formattedEncounters = $encounters->map(function($encounter) {
            return [
                'id' => $encounter->getId(),
                'uuid' => $encounter->getUuid(),
                'homeTeam' => [
                    'id' => $encounter->getHomeTeam()->getId(),
                    'uuid' => $encounter->getHomeTeam()->getUuid(),
                    'name' => $encounter->getHomeTeam()->getName()
                ],
                'awayTeam' => [
                    'id' => $encounter->getAwayTeam()->getId(),
                    'uuid' => $encounter->getAwayTeam()->getUuid(),
                    'name' => $encounter->getAwayTeam()->getName()
                ],
                'homeScore' => $encounter->getHomeScore(),
                'awayScore' => $encounter->getAwayScore(),
                'encounterDate' => $encounter->getEncounterDate()->format('Y-m-d H:i:s'),
                'status' => $encounter->getStatus(),
                'location' => $encounter->getLocation()
            ];
        })->toArray();

        return $this->result([
            'tournamentDay' => $this->formatTournamentDayData($tournamentDay),
            'encounters' => $formattedEncounters
        ]);
    }

    public function getDayStatistics(string $id): GenericResponse
    {
        $tournamentDay = $this->tournamentDayRepository->getByUuid($id);
        if (!$tournamentDay) {
            $message = new GenericResponseMessage('Tournament day not found');
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
        }

        $statistics = [
            'totalEncounters' => $tournamentDay->getEncountersCount(),
            'completedEncounters' => $tournamentDay->getCompletedEncountersCount(),
            'scheduledEncounters' => $tournamentDay->getScheduledEncountersCount(),
            'dayStatus' => $tournamentDay->getDayStatus(),
            'isPast' => $tournamentDay->isPast(),
            'isToday' => $tournamentDay->isToday(),
            'isFuture' => $tournamentDay->isFuture()
        ];

        return $this->result([
            'tournamentDay' => $this->formatTournamentDayData($tournamentDay),
            'statistics' => $statistics
        ]);
    }

    public function getUpcomingDays(Request $request): GenericResponse
    {
        $limit = $request->query->getInt('limit', 10);
        $tournamentDays = $this->tournamentDayRepository->findUpcoming($limit);

        $formattedDays = array_map(function($tournamentDay) {
            return $this->formatTournamentDayData($tournamentDay);
        }, $tournamentDays);

        return $this->result($formattedDays);
    }

    public function getTodaysDays(): GenericResponse
    {
        $tournamentDays = $this->tournamentDayRepository->findForToday();

        $formattedDays = array_map(function($tournamentDay) {
            return $this->formatTournamentDayData($tournamentDay);
        }, $tournamentDays);

        return $this->result($formattedDays);
    }

    public function addEncounterToDay(Request $request): GenericResponse
    {
        // This will be implemented when we update the encounter creation
        return $this->result(null);
    }

    public function removeEncounterFromDay(Request $request): GenericResponse
    {
        // This will be implemented when we update the encounter management
        return $this->result(null);
    }

    private function formatTournamentDayData(TournamentDay $tournamentDay, bool $detailed = false): array
    {
        $data = [
            'id' => $tournamentDay->getId(),
            'uuid' => $tournamentDay->getUuid(),
            'dayNumber' => $tournamentDay->getDayNumber(),
            'name' => $tournamentDay->getName(),
            'description' => $tournamentDay->getDescription(),
            'date' => $tournamentDay->getFormattedDate(),
            'dayStatus' => $tournamentDay->getDayStatus(),
            'encountersCount' => $tournamentDay->getEncountersCount(),
            'active' => $tournamentDay->isActive(),
            'createdAt' => $tournamentDay->getCreatedAt()->format('Y-m-d H:i:s')
        ];

        if ($detailed) {
            $data['tournament'] = [
                'id' => $tournamentDay->getTournament()->getId(),
                'uuid' => $tournamentDay->getTournament()->getUuid(),
                'name' => $tournamentDay->getTournament()->getName()
            ];
        }

        return $data;
    }
}
