<?php

namespace App\Controller\Api\Team;

use App\Service\Team\Implementation\TeamService;
use App\Controller\Api\Shared\AbstractBaseApiController;
use Symfony\Component\HttpFoundation\Request;
use FOS\RestBundle\Controller\Annotations as Rest;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Security;

#[Rest\Route('/teams')]
#[OA\Tag(name: 'Teams')]
class TeamController extends AbstractBaseApiController
{
    private TeamService $service;

    public function __construct(TeamService $service)
    {
        $this->service = $service;
    }

    /**
     * Create a new team
     */
    #[Rest\Post('/create')]

    #[OA\Post(
        path: '/api/v2/teams/create',
        summary: 'Create a new team',
        description: 'Creates a new team for a company'
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            type: "object",
            required: ["name", "categoryId"],
            properties: [
                new OA\Property(property: 'name', type: 'string', example: 'Lions U16'),
                new OA\Property(property: 'categoryId', type: 'string', example: 'uuid-of-team-category', description: 'UUID of the team category'),
                new OA\Property(property: 'companyId', type: 'string', example: 'uuid-of-company', description: 'Optional - admin only. If not provided, uses current user\'s company')
            ]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Team created successfully',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 201),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'team',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'team-uuid'),
                                new OA\Property(property: 'name', type: 'string', example: 'Lions U16'),
                                new OA\Property(
                                    property: 'category',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 1),
                                        new OA\Property(property: 'uuid', type: 'string', example: 'category-uuid'),
                                        new OA\Property(property: 'name', type: 'string', example: 'U16'),
                                        new OA\Property(property: 'code', type: 'string', example: 'U16'),
                                        new OA\Property(property: 'ageRange', type: 'string', example: 'Under 15 years'),
                                        new OA\Property(property: 'gender', type: 'string', example: 'MIXED')
                                    ]
                                ),
                                new OA\Property(
                                    property: 'company',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 1),
                                        new OA\Property(property: 'uuid', type: 'string', example: 'company-uuid'),
                                        new OA\Property(property: 'name', type: 'string', example: 'Lions Football Club')
                                    ]
                                ),
                                new OA\Property(property: 'playersCount', type: 'integer', example: 0),
                                new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Validation error'
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied'
    )]
    #[OA\Response(
        response: 404,
        description: 'Team category or company not found'
    )]
    #[Security(name: 'Bearer')]
    public function createTeam(Request $request)
    {
        return $this->result($this->service->createTeam($request));
    }

    /**
     * Get all teams
     */
    #[Rest\Get('/list')]
    #[OA\Get(
        path: '/api/v2/teams/list',
        summary: 'Get all teams',
        description: 'Retrieves all teams with optional filtering',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'category',
        in: 'query',
        description: 'Filter by category',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'companyId',
        in: 'query',
        description: 'Filter by company',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'active',
        in: 'query',
        description: 'Filter by active status',
        required: false,
        schema: new OA\Schema(type: 'boolean')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of teams',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 200),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'uuid', type: 'string', example: 'team-uuid'),
                            new OA\Property(property: 'name', type: 'string', example: 'Lions U16'),
                            new OA\Property(
                                property: 'category',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'name', type: 'string', example: 'U16'),
                                    new OA\Property(property: 'code', type: 'string', example: 'U16')
                                ]
                            ),
                            new OA\Property(
                                property: 'company',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'name', type: 'string', example: 'Lions FC')
                                ]
                            ),
                            new OA\Property(property: 'playersCount', type: 'integer', example: 18),
                            new OA\Property(property: 'active', type: 'boolean', example: true)
                        ]
                    )
                )
            ]
        )
    )]
    public function getAllTeams(Request $request)
    {
        return $this->result($this->service->getAllTeams($request));
    }

    /**
     * Get team by ID
     */
    #[Rest\Get('/show/{id}')]
    #[OA\Get(
        path: '/api/v2/teams/show/{id}',
        summary: 'Get team by ID',
        description: 'Retrieves a team by ID',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Team UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Team details',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 200),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'uuid', type: 'string', example: 'team-uuid'),
                        new OA\Property(property: 'name', type: 'string', example: 'Lions U16'),
                        new OA\Property(
                            property: 'category',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'category-uuid'),
                                new OA\Property(property: 'name', type: 'string', example: 'U16'),
                                new OA\Property(property: 'code', type: 'string', example: 'U16'),
                                new OA\Property(property: 'ageRange', type: 'string', example: 'Under 15 years'),
                                new OA\Property(property: 'gender', type: 'string', example: 'MIXED')
                            ]
                        ),
                        new OA\Property(
                            property: 'company',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'company-uuid'),
                                new OA\Property(property: 'name', type: 'string', example: 'Lions Football Club')
                            ]
                        ),
                        new OA\Property(
                            property: 'players',
                            type: 'array',
                            items: new OA\Items(
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'uuid', type: 'string', example: 'player-uuid'),
                                    new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                                    new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                                    new OA\Property(property: 'position', type: 'string', example: 'Forward'),
                                    new OA\Property(property: 'jerseyNumber', type: 'integer', example: 10)
                                ]
                            )
                        ),
                        new OA\Property(property: 'playersCount', type: 'integer', example: 18),
                        new OA\Property(property: 'active', type: 'boolean', example: true),
                        new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(response: 404, description: 'Team not found')]
    public function getTeam(string $id)
    {
        return $this->result($this->service->getTeam($id));
    }

    /**
     * Update team
     */
    #[Rest\Put('/update/{id}')]
    #[OA\Put(
        path: '/api/v2/teams/update/{id}',
        summary: 'Update team',
        description: 'Updates a team (company owner or admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Team UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        description: 'Team data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'name', type: 'string'),
                new OA\Property(property: 'categoryId', type: 'string', description: 'UUID of the team category')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Team updated successfully'
    )]
    public function updateTeam(Request $request, string $id)
    {
        return $this->result($this->service->updateTeam($request, $id));
    }

    /**
     * Delete team
     */
    #[Rest\Delete('/delete/{id}')]
    #[OA\Delete(
        path: '/api/v2/teams/delete/{id}',
        summary: 'Delete team',
        description: 'Deletes a team (company owner or admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Team UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Team deleted successfully'
    )]
    public function deleteTeam(string $id)
    {
        return $this->result($this->service->deleteTeam($id));
    }

    /**
     * Get teams by company
     */
    #[Rest\Get('/company/{companyId}')]
    #[OA\Get(
        path: '/api/v2/teams/company/{companyId}',
        summary: 'Get teams by company',
        description: 'Retrieves teams for a specific company',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'companyId',
        in: 'path',
        description: 'Company UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of company teams'
    )]
    public function getTeamsByCompany(string $companyId)
    {
        return $this->result($this->service->getTeamsByCompany($companyId));
    }

    /**
     * Get teams by category
     */
    #[Rest\Get('/category/{category}')]
    #[OA\Get(
        path: '/api/v2/teams/category/{category}',
        summary: 'Get teams by category',
        description: 'Retrieves teams for a specific category',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'category',
        in: 'path',
        description: 'Team category',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of teams in category'
    )]
    public function getTeamsByCategory(string $category)
    {
        return $this->result($this->service->getTeamsByCategory($category));
    }

    /**
     * Get team statistics
     */
    #[Rest\Get('/statistics/{id}')]
    #[OA\Get(
        path: '/api/v2/teams/statistics/{id}',
        summary: 'Get team statistics',
        description: 'Retrieves statistics for a team',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Team UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Team statistics'
    )]
    public function getTeamStatistics(string $id)
    {
        return $this->result($this->service->getTeamStatistics($id));
    }

    /**
     * Get team players
     */
    #[Rest\Get('/players/{id}')]
    #[OA\Get(
        path: '/api/v2/teams/players/{id}',
        summary: 'Get team players',
        description: 'Retrieves players for a team',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Team UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of team players'
    )]
    public function getTeamPlayers(string $id)
    {
        return $this->result($this->service->getTeamPlayers($id));
    }
}
