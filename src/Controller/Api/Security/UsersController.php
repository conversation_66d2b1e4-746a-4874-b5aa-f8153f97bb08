<?php

namespace App\Controller\Api\Security;

use OpenApi\Attributes as OA;
use App\Dto\Security\In\AuthenticateIn;
use Nelmio\ApiDocBundle\Annotation\Model;
use App\Dto\Security\Out\User\UserFullOut;
use App\Dto\Security\In\AuthenticateVerifyIn;
use Symfony\Component\HttpFoundation\Request;
use App\Service\Security\Interface\IUserService;
use App\Controller\Api\Shared\AbstractBaseApiController;
use FOS\RestBundle\Controller\Annotations as Rest;
use App\Service\Security\Implementation\UserService;

#[OA\Tag(name: 'Authentication')]
class UsersController extends AbstractBaseApiController
{
  private UserService $service;

  public function __construct(UserService $userService)
  {
    $this->service = $userService;
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(ref: new Model(type: AuthenticateIn::class))
  )]
  #[Rest\Post("/users/authenticate", name: "api_users_authenticate")]
  public function authenticate(Request $request)
  {
    return $this->result($this->service->authenticate($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(ref: new Model(type: AuthenticateVerifyIn::class))
  )]
  #[Rest\Post("/users/authenticate/verify", name: "api_users_authenticate_verify")]
  public function verify(Request $request)
  {
  }

   /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/registerprofile", name: "api_users_register_profile")]
  public function registerProfile(Request $request)
  {
    return $this->result($this->service->registerProfile($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/updateprofile", name: "api_users_update_profile")]
  public function updateProfile(Request $request)
  {
    return $this->result($this->service->updateProfile($request));
  }

     /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/setlanguage", name: "api_users_set_language")]
  public function setUserLanguage(Request $request)
  {
    return $this->result($this->service->setLanguage($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/removalRequest/{option}", name: "api_users_removal_request")]
  public function removalRequest(string $option)
  {
    return $this->result($this->service->removalRequest($option));
  }

     /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/admin/update", name: "api_admin_update_users")]
  public function updateUserAdmin(Request $request)
  {
    return $this->result($this->service->updateProfileAdmin($request));
  }
  
          /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Delete("/users/admin/delete/{uuid}", name: "api_delete_users_admin")]
  public function deleteUsersAdmin(string $uuid)
  {
    return $this->result($this->service->deleteUsersAdmin($uuid));
  }
  
    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/authenticationcodes/", name: "api_users_get_authentication_codes")]
  public function getAuthCodes()
  {
    return $this->result($this->service->getAllAuthCodes());
  }

  


  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/authenticate/info", name: "api_users_authenticate_info")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "full"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: UserFullOut::class))
    )
  )]
  public function info()
  {
    return $this->result($this->service->info());
  }



  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/admin/super/info", name: "api_users_super_admin_info")]
  public function isSuperAdmin()
  {

    return $this->result($this->service->isSuperAdmin());
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Patch("/users/{uuid}", name: "api_users_update")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "standard"
    ),
  )]
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(
      type: "array",
      items: new OA\Items(
        properties: [
          new OA\Property(property: "type", description: "Patch operation type", type: "string"),
          new OA\Property(property: "payload", description: "Patch operation data value", type: "object")
        ]
      ),
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user',
    content: new OA\JsonContent(ref: new Model(type: UserFullOut::class))
  )]
  public function update(string $uuid, Request $request)
  {
    return $this->result($this->service->update($uuid, $request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/{uuid}", name: "api_users_findOne")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "full"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: UserFullOut::class))
    )
  )]
  public function findOne(string $uuid, Request $request)
  {
    return $this->result($this->service->findOne($uuid, $request));
  }

      /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/info/{id}", name: "api_users_findonebyid")]
  public function findOneUserById(string $id)
  {
    return $this->result($this->service->findOneById($id));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/users/admin/list", name: "api_users_findall_admin")]
  public function getUsersAdmin(Request $request)
  {
    return $this->result($this->service->getUsers($request));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/users/reset_bonus", name: "api_users_reset_bonus")]
  public function resetBonus()
  {
    return $this->result($this->service->resetBonus());
  }

}
