<?php

namespace App\Controller\Api\Security;

use App\Controller\Api\Shared\AbstractBaseApiController;
use App\Dto\Shared\In\QueryFilterIn;
use App\Dto\Security\In\Role\RoleCreateIn;
use App\Dto\Security\In\Role\RoleReplaceOrUpdateIn;
use App\Dto\Security\Out\Role\RoleFullOut;
use App\Service\Security\Interface\IRoleService;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Model;

#[OA\Tag(name: 'Security')]
class RolesController extends AbstractBaseApiController
{
  private IRoleService $service;

  public function __construct(IRoleService $userService)
  {
    $this->service = $userService;
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/roles", name: "api_roles_findAll")]
  #[OA\Parameter(
    name: 'limit',
    in: 'query',
    description: 'Maximum number of results to return.',
    schema: new OA\Schema(type: 'integer')
  )]
  #[OA\Parameter(
    name: 'offset',
    in: 'query',
    description: 'The number of results to skip',
    schema: new OA\Schema(type: 'integer')
  )]
  #[OA\Parameter(
    name: 'search',
    in: 'query',
    description: 'Search keyword',
    schema: new OA\Schema(type: 'string')
  )]
  #[OA\Parameter(
    name: 'filters',
    in: 'query',
    description: 'Query filters',
    schema: new OA\Schema(type: 'array', items: new OA\Items(ref: new Model(type: QueryFilterIn::class)))
  )]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "standard"
    ),
  )]
  #[OA\Parameter(
    name: 'single',
    in: 'query',
    description: 'Single mode will only the first result if many found or null if empty',
    schema: new OA\Schema(
      type: "boolean",
      default: "false"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: RoleFullOut::class))
    )
  )]
  public function findAll(Request $request)
  {
    return $this->result($this->service->findAll($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/roles/{uuid}", name: "api_roles_findOne")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "full"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: RoleFullOut::class))
    )
  )]
  public function findOne(string $uuid, Request $request)
  {
    return $this->result($this->service->findOne($uuid, $request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "standard"
    ),
  )]
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(ref: new Model(type: RoleCreateIn::class))
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user',
    content: new OA\JsonContent(ref: new Model(type: RoleFullOut::class))
  )]
  #[Rest\Post("/roles", name: "api_roles_create")]
  public function create(Request $request)
  {
    return $this->result($this->service->create($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Put("/roles/{uuid}", name: "api_roles_replace")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "standard"
    ),
  )]
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(ref: new Model(type: RoleReplaceOrUpdateIn::class))
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user',
    content: new OA\JsonContent(ref: new Model(type: RoleFullOut::class))
  )]
  public function replace(string $uuid, Request $request)
  {
    return $this->result($this->service->replace($uuid, $request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Patch("/roles/{uuid}", name: "api_roles_update")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "standard"
    ),
  )]
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(
      type: "array",
      items: new OA\Items(
        properties: [
          new OA\Property(property: "type", description: "Patch operation type", type: "string"),
          new OA\Property(property: "payload", description: "Patch operation data value", type: "object")
        ]
      ),
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user',
    content: new OA\JsonContent(ref: new Model(type: RoleFullOut::class))
  )]
  public function update(string $uuid, Request $request)
  {
    return $this->result($this->service->update($uuid, $request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Delete("/roles/{uuid}", name: "api_roles_remove")]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user',
    content: new OA\JsonContent(ref: new Model(type: RoleFullOut::class))
  )]
  public function remove(string $uuid, Request $request)
  {
    return $this->result($this->service->remove($uuid, $request));
  }
}
