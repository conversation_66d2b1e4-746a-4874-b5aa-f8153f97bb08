<?php

namespace App\Service\Team\Implementation;

use App\Dto\GenericResponse;
use App\Dto\GenericResponseMessage;
use App\Constant\GenericResponseStatuses;
use App\Constant\GenericResponseMessageTypes;
use App\Entity\Team\Team;
use App\Repository\Team\TeamRepository;
use App\Repository\Team\TeamCategoryRepository;
use App\Repository\Company\CompanyRepository;
use App\Service\Team\Interface\ITeamService;
use App\Service\Traits\BaseServiceTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Security;

class TeamService implements ITeamService
{
    use BaseServiceTrait;

    private TeamRepository $teamRepository;
    private TeamCategoryRepository $teamCategoryRepository;
    private CompanyRepository $companyRepository;
    private EntityManagerInterface $entityManager;
    private Security $security;

    public function __construct(
        TeamRepository $teamRepository,
        TeamCategoryRepository $teamCategoryRepository,
        CompanyRepository $companyRepository,
        EntityManagerInterface $entityManager,
        Security $security
    ) {
        $this->teamRepository = $teamRepository;
        $this->teamCategoryRepository = $teamCategoryRepository;
        $this->companyRepository = $companyRepository;
        $this->entityManager = $entityManager;
        $this->security = $security;
    }

    public function createTeam(Request $request): GenericResponse
    {
        try {
            $user = $this->security->getUser();
            if (!$user) {
                $message = new GenericResponseMessage('Authentication required', 'authentication_required', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_UNAUTHORIZED, [$message]);
            }

            $data = json_decode($request->getContent(), true);

            // Validate required fields
            if (empty($data['name'])) {
                $message = new GenericResponseMessage('Team name is required', 'validation_error', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
            }

            if (empty($data['categoryId'])) {
                $message = new GenericResponseMessage('Team category ID is required', 'validation_error', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
            }

            // Get team category
            $category = $this->teamCategoryRepository->getByUuid($data['categoryId']);
            if (!$category) {
                $message = new GenericResponseMessage('Team category not found', 'category_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            // Get company - either from request or user's company
            $company = null;
            if (isset($data['companyId'])) {
                // Admin can specify company
                if (!$this->isAdmin()) {
                    $message = new GenericResponseMessage('Access denied. Admin privileges required to specify company.', 'access_denied', GenericResponseMessageTypes::ERROR);
                    return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
                }
                $company = $this->companyRepository->findOneBy(['uuid' => $data['companyId'], 'active' => true]);
            } else {
                // Company owner creates team for their own company
                $company = $user->getCompany();
            }

            if (!$company) {
                $message = new GenericResponseMessage('Company not found or user not associated with a company', 'company_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            // Check if user is company owner or admin
            if (!$this->isAdmin() && $user->getCompany() !== $company) {
                $message = new GenericResponseMessage('Access denied. You can only create teams for your own company.', 'access_denied', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $team = new Team();
            $team->setName($data['name'])
                 ->setCategory($category)
                 ->setCompany($company)
                 ->setActive(true);

            $this->teamRepository->save($team, true);

            return $this->result([
                'team' => $this->formatTeamData($team)
            ], GenericResponseStatuses::SUCCESS, Response::HTTP_CREATED);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error creating team: ' . $e->getMessage(), 'server_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getAllTeams(Request $request): GenericResponse
    {
        try {
            $categoryId = $request->query->get('category');
            $companyId = $request->query->get('companyId');
            $active = $request->query->getBoolean('active', true);

            $criteria = ['active' => $active];

            // If category filter is provided, find the category entity
            if ($categoryId) {
                $categoryEntity = $this->teamCategoryRepository->getByUuid($categoryId);
                if (!$categoryEntity) {
                    $message = new GenericResponseMessage('Team category not found', 'category_not_found', GenericResponseMessageTypes::ERROR);
                    return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
                }
                $criteria['category'] = $categoryEntity;
            }

            $teams = $this->teamRepository->findBy($criteria, ['name' => 'ASC']);

            // Filter by company if specified
            if ($companyId) {
                $teams = array_filter($teams, function($team) use ($companyId) {
                    return $team->getCompany() && $team->getCompany()->getUuid() === $companyId;
                });
            }

            $formattedTeams = array_map(
                fn($team) => $this->formatTeamData($team),
                $teams
            );

            return $this->result([
                'teams' => $formattedTeams,
                'total' => count($formattedTeams)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching teams: ' . $e->getMessage());
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getTeam(string $id): GenericResponse
    {
        try {
            $team = $this->teamRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$team) {
                $message = new GenericResponseMessage('Team not found');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            return $this->result([
                'team' => $this->formatTeamData($team, true)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching team: ' . $e->getMessage());
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function updateTeam(Request $request, string $id): GenericResponse
    {
        try {
            $user = $this->security->getUser();
            if (!$user) {
                $message = new GenericResponseMessage('Authentication required');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_UNAUTHORIZED, [$message]);
            }

            $team = $this->teamRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$team) {
                $message = new GenericResponseMessage('Team not found');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            // Check if user is company owner or admin
            if (!$this->isAdmin() && $user->getCompany() !== $team->getCompany()) {
                $message = new GenericResponseMessage('Access denied. You can only update teams from your own company.');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $data = json_decode($request->getContent(), true);

            if (isset($data['name'])) {
                $team->setName($data['name']);
            }

            if (isset($data['categoryId'])) {
                $category = $this->teamCategoryRepository->getByUuid($data['categoryId']);
                if (!$category) {
                    $message = new GenericResponseMessage('Team category not found', 'category_not_found', GenericResponseMessageTypes::ERROR);
                    return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
                }
                $team->setCategory($category);
            }

            $this->teamRepository->save($team, true);

            return $this->result([
                'team' => $this->formatTeamData($team)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error updating team: ' . $e->getMessage());
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function deleteTeam(string $id): GenericResponse
    {
        try {
            $user = $this->security->getUser();
            if (!$user) {
                $message = new GenericResponseMessage('Authentication required');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_UNAUTHORIZED, [$message]);
            }

            $team = $this->teamRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$team) {
                $message = new GenericResponseMessage('Team not found');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            // Check if user is company owner or admin
            if (!$this->isAdmin() && $user->getCompany() !== $team->getCompany()) {
                $message = new GenericResponseMessage('Access denied. You can only delete teams from your own company.');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $team->setActive(false);
            $this->teamRepository->save($team, true);

            return $this->result([]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error deleting team: ' . $e->getMessage());
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getTeamsByCompany(string $companyId): GenericResponse
    {
        try {
            $company = $this->companyRepository->findOneBy(['uuid' => $companyId, 'active' => true]);

            if (!$company) {
                $message = new GenericResponseMessage('Company not found');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            $teams = $this->teamRepository->findBy(['company' => $company, 'active' => true], ['name' => 'ASC']);

            $formattedTeams = array_map(
                fn($team) => $this->formatTeamData($team),
                $teams
            );

            return $this->result([
                'teams' => $formattedTeams,
                'total' => count($formattedTeams)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching teams: ' . $e->getMessage());
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getTeamsByCategory(string $category): GenericResponse
    {
        try {
            // Find the category first
            $categoryEntity = $this->teamCategoryRepository->getByUuid($category);
            if (!$categoryEntity) {
                $message = new GenericResponseMessage('Team category not found', 'category_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            $teams = $this->teamRepository->findBy(['category' => $categoryEntity, 'active' => true], ['name' => 'ASC']);

            $formattedTeams = array_map(
                fn($team) => $this->formatTeamData($team),
                $teams
            );

            return $this->result([
                'teams' => $formattedTeams,
                'total' => count($formattedTeams)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching teams: ' . $e->getMessage(), 'teams_fetch_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getTeamStatistics(string $id): GenericResponse
    {
        try {
            $team = $this->teamRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$team) {
                $message = new GenericResponseMessage('Team not found');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            // Calculate team statistics
            $players = $team->getPlayers();
            $totalGoals = 0;
            $totalYellowCards = 0;
            $totalRedCards = 0;
            $totalAssists = 0;
            $totalMatches = 0;

            foreach ($players as $player) {
                $totalGoals += $player->getTotalGoals() ?? 0;
                $totalYellowCards += $player->getTotalYellowCards() ?? 0;
                $totalRedCards += $player->getTotalRedCards() ?? 0;
                $totalAssists += $player->getTotalAssists() ?? 0;
                $totalMatches += $player->getTotalMatches() ?? 0;
            }

            $statistics = [
                'totalPlayers' => $players->count(),
                'totalGoals' => $totalGoals,
                'totalYellowCards' => $totalYellowCards,
                'totalRedCards' => $totalRedCards,
                'totalAssists' => $totalAssists,
                'totalMatches' => $totalMatches,
                'averageGoalsPerPlayer' => $players->count() > 0 ? round($totalGoals / $players->count(), 2) : 0,
            ];

            return $this->result([
                'team' => $this->formatTeamData($team),
                'statistics' => $statistics
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching team statistics: ' . $e->getMessage());
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getTeamPlayers(string $id): GenericResponse
    {
        try {
            $team = $this->teamRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$team) {
                $message = new GenericResponseMessage('Team not found');
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            $players = $team->getPlayers()->filter(fn($player) => $player->isActive());

            $formattedPlayers = $players->map(function($player) {
                return [
                    'id' => $player->getId(),
                    'uuid' => $player->getUuid(),
                    'firstName' => $player->getFirstName(),
                    'lastName' => $player->getLastName(),
                    'position' => $player->getPosition(),
                    'jerseyNumber' => $player->getJerseyNumber(),
                    'totalGoals' => $player->getTotalGoals(),
                    'totalYellowCards' => $player->getTotalYellowCards(),
                    'totalRedCards' => $player->getTotalRedCards(),
                    'totalAssists' => $player->getTotalAssists(),
                    'totalMatches' => $player->getTotalMatches(),
                ];
            })->toArray();

            return $this->result([
                'team' => $this->formatTeamData($team),
                'players' => $formattedPlayers,
                'total' => count($formattedPlayers)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching team players: ' . $e->getMessage());
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    private function formatTeamData(Team $team, bool $detailed = false): array
    {
        $data = [
            'id' => $team->getId(),
            'uuid' => $team->getUuid(),
            'name' => $team->getName(),
            'category' => $team->getCategory() ? [
                'id' => $team->getCategory()->getId(),
                'uuid' => $team->getCategory()->getUuid(),
                'name' => $team->getCategory()->getName(),
                'code' => $team->getCategory()->getCode(),
                'gender' => $team->getCategory()->getGender(),
            ] : null,
            'company' => [
                'id' => $team->getCompany()->getId(),
                'uuid' => $team->getCompany()->getUuid(),
                'name' => $team->getCompany()->getName(),
            ],
            'playersCount' => $team->getPlayers()->count(),
            'createdAt' => $team->getCreatedAt()?->format('Y-m-d H:i:s'),
        ];

        if ($detailed) {
            $data['players'] = $team->getPlayers()->map(function($player) {
                return [
                    'id' => $player->getId(),
                    'uuid' => $player->getUuid(),
                    'firstName' => $player->getFirstName(),
                    'lastName' => $player->getLastName(),
                    'position' => $player->getPosition(),
                    'jerseyNumber' => $player->getJerseyNumber(),
                ];
            })->toArray();
        }

        return $data;
    }

    private function isAdmin(): bool
    {
        $user = $this->security->getUser();
        if (!$user) return false;

        // Check if user has admin meta
        foreach ($user->getMetas() as $meta) {
            if ($meta->getCode() === 'is_admin' && $meta->getValue() === '1') {
                return true;
            }
            if ($meta->getCode() === 'is_super_admin' && $meta->getValue() === '1') {
                return true;
            }
        }

        return false;
    }
}
