<?php

namespace App\Controller\Api\Company;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Request;
use App\Controller\Api\Shared\AbstractBaseApiController;
use FOS\RestBundle\Controller\Annotations as Rest;
use App\Service\Company\Implementation\CompanyService;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[OA\Tag(name: 'Companies')]
class CompanyController extends AbstractBaseApiController
{
    private CompanyService $service;

  
    public function __construct(CompanyService $service)
    {
      $this->service = $service;

    }

      /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/companies/createcompany", name: "api_companies_create_company")]
  public function createCompany(Request $request)
  {
    return $this->result($this->service->createCompany($request));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/companies/updatecompany", name: "api_companies_update_company")]
  public function updateCompany(Request $request)
  {
    return $this->result($this->service->updateCompany($request));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/companies/business/{uuid}", name: "api_companies_get_business")]
  public function business(string $uuid)
  {
    return $this->result($this->service->business($uuid));
  }

     /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/companies/admin/update", name: "api_admin_update_company")]
  public function updateCompanyAdmin(Request $request)
  {
    return $this->result($this->service->updateCompanyAdmin($request));
  }


        /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Delete("/companies/admin/delete/{uuid}", name: "api_delete_companies_admin")]
  public function deleteCompaniesAdmin(string $uuid)
  {
    return $this->result($this->service->deleteCompanyAdmin($uuid));
  }

  
  
}
