<?php

namespace App\DataFixtures\Team;

use App\Entity\Team\TeamCategory;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\Uid\Uuid;

class TeamCategoryFixtures extends Fixture
{
    public const CATEGORY_U13 = 'category_u13';
    public const CATEGORY_U14 = 'category_u14';
    public const CATEGORY_U15 = 'category_u15';
    public const CATEGORY_U16 = 'category_u16';
    public const CATEGORY_U17 = 'category_u17';

    private function findExistingCategory(ObjectManager $manager, string $code): ?TeamCategory
    {
        return $manager->getRepository(TeamCategory::class)->findOneBy(['code' => $code]);
    }

    public function load(ObjectManager $manager): void
    {
        $categories = [
            ['name' => 'Under 13', 'code' => 'U13', 'minAge' => 11, 'maxAge' => 13],
            ['name' => 'Under 14', 'code' => 'U14', 'minAge' => 12, 'maxAge' => 14],
            ['name' => 'Under 15', 'code' => 'U15', 'minAge' => 13, 'maxAge' => 15],
            ['name' => 'Under 16', 'code' => 'U16', 'minAge' => 14, 'maxAge' => 16],
            ['name' => 'Under 17', 'code' => 'U17', 'minAge' => 15, 'maxAge' => 17],
        ];

        foreach ($categories as $index => $categoryData) {
            $category = $this->findExistingCategory($manager, $categoryData['code']);
            
            if (!$category) {
                $category = new TeamCategory();
                // UUID is only set for new categories
            }
            
            // Update category properties
            $category->setName($categoryData['name']);
            $category->setCode($categoryData['code']);
            $category->setMinAge($categoryData['minAge']);
            $category->setMaxAge($categoryData['maxAge']);
            $category->setMaxPlayersPerTeam(23);
            $category->setMinPlayersPerTeam(11);
            $category->setGender('MALE');
            $category->setActive(true);
            $category->setUuid(Uuid::v4());
            
            $manager->persist($category);
            
            $this->addReference('category_' . strtolower($categoryData['code']), $category);
        }

        $manager->flush();
    }
}