<?php

namespace App\Controller\Api\Tournament;

use App\Service\Tournament\Implementation\EncounterService;
use App\Controller\Api\Shared\AbstractBaseApiController;
use Symfony\Component\HttpFoundation\Request;
use FOS\RestBundle\Controller\Annotations as Rest;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Security;

#[Rest\Route('/encounters')]
#[OA\Tag(name: 'Encounters')]
class EncounterController extends AbstractBaseApiController
{
    private EncounterService $service;

    public function __construct(EncounterService $service)
    {
        $this->service = $service;
    }

    /**
     * Create a new encounter (admin only)
     */
    #[Rest\Post('/create')]
    #[OA\Tag(name: 'Encounters')]
    #[OA\Post(
        path: '/api/v2/encounters/create',
        summary: 'Create a new encounter',
        description: 'Creates a new encounter between two teams (admin only)'
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            type: "object",
            required: ["homeTeamId", "awayTeamId", "encounterDate", "tournamentDayId"],
            properties: [
                new OA\Property(property: 'homeTeamId', type: 'string', example: 'home-team-uuid', description: 'UUID of the home team'),
                new OA\Property(property: 'awayTeamId', type: 'string', example: 'away-team-uuid', description: 'UUID of the away team'),
                new OA\Property(property: 'encounterDate', type: 'string', format: 'date-time', example: '2024-06-15 15:00:00', description: 'Date and time of the encounter'),
                new OA\Property(property: 'location', type: 'string', example: 'Main Stadium', description: 'Location where the encounter takes place'),
                new OA\Property(property: 'tournamentDayId', type: 'string', example: 'tournament-day-uuid', description: 'UUID of the tournament day (required)'),
                new OA\Property(property: 'status', type: 'string', enum: ['SCHEDULED', 'LIVE', 'COMPLETED', 'CANCELLED'], example: 'SCHEDULED'),
                new OA\Property(property: 'notes', type: 'string', example: 'Important match for championship', description: 'Additional notes about the encounter')
            ]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Encounter created successfully',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 201),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'encounter',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'encounter-uuid'),
                                new OA\Property(
                                    property: 'homeTeam',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 1),
                                        new OA\Property(property: 'uuid', type: 'string', example: 'home-team-uuid'),
                                        new OA\Property(property: 'name', type: 'string', example: 'Lions U16')
                                    ]
                                ),
                                new OA\Property(
                                    property: 'awayTeam',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 2),
                                        new OA\Property(property: 'uuid', type: 'string', example: 'away-team-uuid'),
                                        new OA\Property(property: 'name', type: 'string', example: 'Eagles U16')
                                    ]
                                ),
                                new OA\Property(property: 'homeScore', type: 'integer', example: 0),
                                new OA\Property(property: 'awayScore', type: 'integer', example: 0),
                                new OA\Property(
                                    property: 'category',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 1),
                                        new OA\Property(property: 'uuid', type: 'string', example: 'category-uuid'),
                                        new OA\Property(property: 'name', type: 'string', example: 'U16'),
                                        new OA\Property(property: 'code', type: 'string', example: 'U16')
                                    ]
                                ),
                                new OA\Property(property: 'encounterDate', type: 'string', example: '2024-06-15 15:00:00'),
                                new OA\Property(property: 'location', type: 'string', example: 'Main Stadium'),
                                new OA\Property(property: 'status', type: 'string', example: 'SCHEDULED'),
                                new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Validation error - Teams must be from same category'
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin privileges required'
    )]
    #[OA\Response(
        response: 404,
        description: 'One or both teams not found'
    )]
    #[Security(name: 'Bearer')]
    public function createEncounter(Request $request)
    {
        return $this->result($this->service->createEncounter($request));
    }

    /**
     * Get all encounters
     */
    #[Rest\Get('/list')]
    #[OA\Get(
        path: '/api/v2/encounters/list',
        summary: 'Get all encounters',
        description: 'Retrieves all encounters with optional filtering',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'status',
        in: 'query',
        description: 'Filter by status',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'categoryId',
        in: 'query',
        description: 'Filter by team category UUID',
        required: false,
        schema: new OA\Schema(type: 'string'),
        example: 'category-uuid'
    )]
    #[OA\Parameter(
        name: 'tournamentId',
        in: 'query',
        description: 'Filter by tournament',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of encounters',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 200),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'uuid', type: 'string', example: 'encounter-uuid'),
                            new OA\Property(
                                property: 'homeTeam',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'uuid', type: 'string', example: 'home-team-uuid'),
                                    new OA\Property(property: 'name', type: 'string', example: 'Lions U16')
                                ]
                            ),
                            new OA\Property(
                                property: 'awayTeam',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 2),
                                    new OA\Property(property: 'uuid', type: 'string', example: 'away-team-uuid'),
                                    new OA\Property(property: 'name', type: 'string', example: 'Eagles U16')
                                ]
                            ),
                            new OA\Property(property: 'homeScore', type: 'integer', example: 2),
                            new OA\Property(property: 'awayScore', type: 'integer', example: 1),
                            new OA\Property(
                                property: 'category',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'uuid', type: 'string', example: 'category-uuid'),
                                    new OA\Property(property: 'name', type: 'string', example: 'U16'),
                                    new OA\Property(property: 'code', type: 'string', example: 'U16')
                                ]
                            ),
                            new OA\Property(property: 'encounterDate', type: 'string', example: '2024-06-15 15:00:00'),
                            new OA\Property(property: 'location', type: 'string', example: 'Main Stadium'),
                            new OA\Property(property: 'status', type: 'string', example: 'COMPLETED'),
                            new OA\Property(property: 'notes', type: 'string', example: 'Great match'),
                            new OA\Property(
                                property: 'tournament',
                                type: 'object',
                                nullable: true,
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'uuid', type: 'string', example: 'tournament-uuid'),
                                    new OA\Property(property: 'name', type: 'string', example: 'Summer Championship 2024')
                                ]
                            ),
                            new OA\Property(property: 'eventsCount', type: 'integer', example: 5),
                            new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                        ]
                    )
                )
            ]
        )
    )]
    public function getAllEncounters(Request $request)
    {
        return $this->result($this->service->getAllEncounters($request));
    }

    /**
     * Get encounter by ID
     */
    #[Rest\Get('/show/{id}')]
    #[OA\Get(
        path: '/api/v2/encounters/show/{id}',
        summary: 'Get encounter by ID',
        description: 'Retrieves an encounter by ID',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Encounter UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Encounter details',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 200),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'uuid', type: 'string', example: 'encounter-uuid'),
                        new OA\Property(
                            property: 'homeTeam',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'home-team-uuid'),
                                new OA\Property(property: 'name', type: 'string', example: 'Lions U16'),
                                new OA\Property(
                                    property: 'players',
                                    type: 'array',
                                    items: new OA\Items(
                                        type: 'object',
                                        properties: [
                                            new OA\Property(property: 'id', type: 'integer', example: 1),
                                            new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                                            new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                                            new OA\Property(property: 'jerseyNumber', type: 'integer', example: 10)
                                        ]
                                    )
                                )
                            ]
                        ),
                        new OA\Property(
                            property: 'awayTeam',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 2),
                                new OA\Property(property: 'uuid', type: 'string', example: 'away-team-uuid'),
                                new OA\Property(property: 'name', type: 'string', example: 'Eagles U16'),
                                new OA\Property(
                                    property: 'players',
                                    type: 'array',
                                    items: new OA\Items(
                                        type: 'object',
                                        properties: [
                                            new OA\Property(property: 'id', type: 'integer', example: 2),
                                            new OA\Property(property: 'firstName', type: 'string', example: 'Jane'),
                                            new OA\Property(property: 'lastName', type: 'string', example: 'Smith'),
                                            new OA\Property(property: 'jerseyNumber', type: 'integer', example: 9)
                                        ]
                                    )
                                )
                            ]
                        ),
                        new OA\Property(property: 'homeScore', type: 'integer', example: 2),
                        new OA\Property(property: 'awayScore', type: 'integer', example: 1),
                        new OA\Property(
                            property: 'events',
                            type: 'array',
                            items: new OA\Items(
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'type', type: 'string', example: 'GOAL'),
                                    new OA\Property(property: 'minute', type: 'integer', example: 25),
                                    new OA\Property(property: 'description', type: 'string', example: 'Great shot'),
                                    new OA\Property(property: 'player', type: 'string', example: 'John Doe'),
                                    new OA\Property(property: 'assistPlayer', type: 'string', example: 'Jane Smith')
                                ]
                            )
                        ),
                        new OA\Property(property: 'encounterDate', type: 'string', example: '2024-06-15 15:00:00'),
                        new OA\Property(property: 'location', type: 'string', example: 'Main Stadium'),
                        new OA\Property(property: 'status', type: 'string', example: 'COMPLETED'),
                        new OA\Property(property: 'notes', type: 'string', example: 'Great match'),
                        new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(response: 404, description: 'Encounter not found')]
    public function getEncounter(string $id)
    {
        return $this->result($this->service->getEncounter($id));
    }

    /**
     * Update encounter (admin only)
     */
    #[Rest\Put('/update/{id}')]
    #[OA\Put(
        path: '/api/v2/encounters/update/{id}',
        summary: 'Update encounter',
        description: 'Updates an encounter (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Encounter UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        description: 'Encounter data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'encounterDate', type: 'string', format: 'date-time'),
                new OA\Property(property: 'location', type: 'string'),
                new OA\Property(property: 'status', type: 'string'),
                new OA\Property(property: 'notes', type: 'string')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Encounter updated successfully'
    )]
    public function updateEncounter(Request $request, string $id)
    {
        return $this->result($this->service->updateEncounter($request, $id));
    }

    /**
     * Delete encounter (admin only)
     */
    #[Rest\Delete('/delete/{id}')]
    #[OA\Delete(
        path: '/api/v2/encounters/delete/{id}',
        summary: 'Delete encounter',
        description: 'Deletes an encounter (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Encounter UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Encounter deleted successfully'
    )]
    public function deleteEncounter(string $id)
    {
        return $this->result($this->service->deleteEncounter($id));
    }

    /**
     * Add event to encounter (admin only) - goals, cards, etc.
     */
    #[Rest\Post('/add-event')]
    #[OA\Post(
        path: '/api/v2/encounters/add-event',
        summary: 'Add event to encounter',
        description: 'Adds an event (goal, card, etc.) to an encounter (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            type: "object",
            required: ["encounterId", "playerId", "type"],
            properties: [
                new OA\Property(property: 'encounterId', type: 'string', example: 'encounter-uuid', description: 'UUID of the encounter'),
                new OA\Property(property: 'playerId', type: 'string', example: 'player-uuid', description: 'UUID of the player'),
                new OA\Property(property: 'type', type: 'string', enum: ['GOAL', 'YELLOW_CARD', 'RED_CARD'], example: 'GOAL', description: 'Type of event'),
                new OA\Property(property: 'minute', type: 'integer', example: 25, description: 'Minute when the event occurred'),
                new OA\Property(property: 'description', type: 'string', example: 'Great shot from outside the box', description: 'Optional description of the event'),
                new OA\Property(property: 'assistPlayerId', type: 'string', example: 'assist-player-uuid', description: 'UUID of assist player (for goals only)')
            ]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Event added successfully',
        content: new OA\JsonContent(
            type: "object",
            properties: [
                new OA\Property(property: 'code', type: 'integer', example: 201),
                new OA\Property(property: 'status', type: 'string', example: 'success'),
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'event',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'uuid', type: 'string', example: 'event-uuid'),
                                new OA\Property(property: 'type', type: 'string', example: 'GOAL'),
                                new OA\Property(property: 'minute', type: 'integer', example: 25),
                                new OA\Property(property: 'description', type: 'string', example: 'Great shot from outside the box'),
                                new OA\Property(
                                    property: 'player',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 1),
                                        new OA\Property(property: 'uuid', type: 'string', example: 'player-uuid'),
                                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                                    ]
                                ),
                                new OA\Property(
                                    property: 'assistPlayer',
                                    type: 'object',
                                    nullable: true,
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 2),
                                        new OA\Property(property: 'uuid', type: 'string', example: 'assist-player-uuid'),
                                        new OA\Property(property: 'firstName', type: 'string', example: 'Jane'),
                                        new OA\Property(property: 'lastName', type: 'string', example: 'Smith')
                                    ]
                                ),
                                new OA\Property(property: 'createdAt', type: 'string', example: '2024-05-30 16:45:00')
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Validation error - Player not part of either team'
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin privileges required'
    )]
    #[OA\Response(
        response: 404,
        description: 'Encounter or player not found'
    )]
    public function addEventToEncounter(Request $request)
    {
        return $this->result($this->service->addEventToEncounter($request));
    }

    /**
     * Get upcoming encounters
     */
    #[Rest\Get('/upcoming')]
    #[OA\Get(
        path: '/api/v2/encounters/upcoming',
        summary: 'Get upcoming encounters',
        description: 'Retrieves upcoming encounters',
        security: [['Bearer' => []]]
    )]
    #[OA\Response(
        response: 200,
        description: 'List of upcoming encounters'
    )]
    public function getUpcomingEncounters()
    {
        return $this->result($this->service->getUpcomingEncounters());
    }

    /**
     * Get completed encounters
     */
    #[Rest\Get('/completed')]
    #[OA\Get(
        path: '/api/v2/encounters/completed',
        summary: 'Get completed encounters',
        description: 'Retrieves completed encounters',
        security: [['Bearer' => []]]
    )]
    #[OA\Response(
        response: 200,
        description: 'List of completed encounters'
    )]
    public function getCompletedEncounters()
    {
        return $this->result($this->service->getCompletedEncounters());
    }

    /**
     * Get encounters by team
     */
    #[Rest\Get('/team/{teamId}')]
    #[OA\Get(
        path: '/api/v2/encounters/team/{teamId}',
        summary: 'Get encounters by team',
        description: 'Retrieves encounters for a specific team',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'teamId',
        in: 'path',
        description: 'Team UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of team encounters'
    )]
    public function getEncountersByTeam(string $teamId)
    {
        return $this->result($this->service->getEncountersByTeam($teamId));
    }

    /**
     * Get encounters by tournament
     */
    #[Rest\Get('/tournament/{tournamentId}')]
    #[OA\Get(
        path: '/api/v2/encounters/tournament/{tournamentId}',
        summary: 'Get encounters by tournament',
        description: 'Retrieves encounters for a specific tournament',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'tournamentId',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of tournament encounters'
    )]
    public function getEncountersByTournament(string $tournamentId)
    {
        return $this->result($this->service->getEncountersByTournament($tournamentId));
    }
}
