<?php

namespace App\DataFixtures\Player;

use App\Entity\Player\Player;
use App\DataFixtures\Team\TeamFixtures;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use <PERSON>ymfony\Component\Uid\Uuid;

class PlayerFixtures extends Fixture implements DependentFixtureInterface
{
    private array $firstNames = [
        '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
    ];

    private array $lastNames = [
        'Eto\'o', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>og<PERSON>', '<PERSON>ah', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>\'<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'W<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ogo<PERSON>', '<PERSON><PERSON>', '<PERSON>ambo'
    ];

    private function findExistingPlayer(ObjectManager $manager, string $firstName, string $lastName, string $teamReference): ?Player
    {
        $team = $this->getReference($teamReference);
        return $manager->getRepository(Player::class)->findOneBy([
            'firstName' => $firstName,
            'lastName' => $lastName,
            'team' => $team
        ]);
    }

    public function load(ObjectManager $manager): void
    {
        $companies = [
            '43e814c7-4af5-4484-97e9-7d20e8e97b46',
            'acfc2abb-3bf8-4a30-88db-1f5114443172',
            '2817d896-a60a-4704-a73d-bcebf817fd99',
            '508a3718-469b-40da-8079-6a54be1b0cd0',
            '836d8364-5b23-4fc8-a7a7-a12458c28a8e',
            'ee3c5fea-fd38-43cc-8ded-39639546a7bd',
            '01310020-ed5e-434c-b074-4ab5121a1fb8',
            'dd1dd5ab-bce7-4e4d-ac71-e74cb96dd2cd',
            '8cb6a894-044c-4daa-872c-c74230ce361f'
        ];

        $categories = ['u13', 'u14', 'u15', 'u16', 'u17'];

        foreach ($companies as $companyUuid) {
            foreach ($categories as $category) {
                // Create 15-20 players per team
                $numPlayers = rand(15, 20);
                for ($i = 0; $i < $numPlayers; $i++) {
                    $firstName = $this->firstNames[array_rand($this->firstNames)];
                    $lastName = $this->lastNames[array_rand($this->lastNames)];
                    $teamReference = 'team_' . $companyUuid . '_' . $category;
                    
                    $player = $this->findExistingPlayer($manager, $firstName, $lastName, $teamReference);
                    
                    if (!$player) {
                        $player = new Player();
                        // UUID is only set for new players
                        $player->setTotalGoals(0);
                        $player->setTotalYellowCards(0);
                        $player->setTotalRedCards(0);
                        $player->setTotalAssists(0);
                        $player->setTotalMatches(0);
                    }
                    
                    // Update player properties
                    $player->setFirstName($firstName);
                    $player->setLastName($lastName);
                    $player->setTeam($this->getReference('team_' . $companyUuid . '_' . $category));
                    $player->setActive(true);
                    $player->setUuid(Uuid::v4());
                    
                    // Set birthdate based on category
                    $year = 2025 - (int)substr($category, 1); // Calculate birth year based on category age
                    $month = rand(1, 12);
                    $day = rand(1, 28);
                    $player->setBirthDate(new \DateTime("$year-$month-$day"));

                    // Set position from available positions
                    $positions = ['Forward', 'Midfielder', 'Defender', 'Goalkeeper'];
                    $player->setPosition($positions[array_rand($positions)]);
                    $player->setJerseyNumber($i + 1);
                    $player->setStatus('REGULAR');
                    // Statistics are only initialized for new players
                    
                    $manager->persist($player);
                }
            }
        }

        $manager->flush();
    }

    public function getDependencies(): array
    {
        return [
            TeamFixtures::class,
        ];
    }
}