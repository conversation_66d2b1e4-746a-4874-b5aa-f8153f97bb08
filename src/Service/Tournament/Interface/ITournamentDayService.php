<?php

namespace App\Service\Tournament\Interface;

use App\Dto\GenericResponse;
use Symfony\Component\HttpFoundation\Request;

interface ITournamentDayService
{
    /**
     * Create a new tournament day
     */
    public function createTournamentDay(Request $request): GenericResponse;

    /**
     * Get all tournament days for a tournament
     */
    public function getTournamentDays(string $tournamentId): GenericResponse;

    /**
     * Get tournament day by ID
     */
    public function getTournamentDay(string $id): GenericResponse;

    /**
     * Update tournament day
     */
    public function updateTournamentDay(Request $request, string $id): GenericResponse;

    /**
     * Delete tournament day
     */
    public function deleteTournamentDay(string $id): GenericResponse;

    /**
     * Get encounters for a specific tournament day
     */
    public function getDayEncounters(string $id): GenericResponse;

    /**
     * Get tournament day statistics
     */
    public function getDayStatistics(string $id): GenericResponse;

    /**
     * Get upcoming tournament days
     */
    public function getUpcomingDays(Request $request): GenericResponse;

    /**
     * Get today's tournament days
     */
    public function getTodaysDays(): GenericResponse;

    /**
     * Add encounter to tournament day
     */
    public function addEncounterToDay(Request $request): GenericResponse;

    /**
     * Remove encounter from tournament day
     */
    public function removeEncounterFromDay(Request $request): GenericResponse;
}
